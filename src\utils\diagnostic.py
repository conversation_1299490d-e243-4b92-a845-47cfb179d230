"""
诊断工具模块
用于检查系统环境和设备状态
"""

import subprocess
import sys
import os
import platform
from typing import Dict, List, Tuple


class SystemDiagnostic:
    """系统诊断工具"""
    
    def __init__(self):
        self.results = {}
    
    def run_full_diagnostic(self) -> Dict[str, any]:
        """
        运行完整的系统诊断
        
        Returns:
            诊断结果字典
        """
        print("🔍 开始系统诊断...")
        print("=" * 50)
        
        # 检查Python环境
        self._check_python_environment()
        
        # 检查ADB工具
        self._check_adb_tool()
        
        # 检查设备连接
        self._check_device_connection()
        
        # 检查依赖包
        self._check_dependencies()
        
        # 检查文件权限
        self._check_file_permissions()
        
        # 生成诊断报告
        self._generate_report()
        
        return self.results
    
    def _check_python_environment(self) -> None:
        """检查Python环境"""
        print("📋 检查Python环境...")
        
        try:
            python_version = sys.version
            python_executable = sys.executable
            
            self.results['python'] = {
                'version': python_version,
                'executable': python_executable,
                'platform': platform.platform(),
                'status': 'OK'
            }
            
            print(f"  ✅ Python版本: {python_version.split()[0]}")
            print(f"  ✅ Python路径: {python_executable}")
            print(f"  ✅ 系统平台: {platform.platform()}")
            
        except Exception as e:
            self.results['python'] = {'status': 'ERROR', 'error': str(e)}
            print(f"  ❌ Python环境检查失败: {e}")
    
    def _check_adb_tool(self) -> None:
        """检查ADB工具"""
        print("\n🔧 检查ADB工具...")
        
        try:
            # 检查ADB是否可用
            result = subprocess.run("adb version", shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                adb_version = result.stdout.strip()
                self.results['adb'] = {
                    'version': adb_version,
                    'status': 'OK'
                }
                print(f"  ✅ ADB工具可用")
                print(f"  ✅ 版本信息: {adb_version.split()[0] if adb_version else 'Unknown'}")
            else:
                self.results['adb'] = {
                    'status': 'ERROR',
                    'error': 'ADB命令执行失败',
                    'stderr': result.stderr
                }
                print(f"  ❌ ADB工具不可用")
                print(f"  ❌ 错误信息: {result.stderr}")
                
        except FileNotFoundError:
            self.results['adb'] = {
                'status': 'ERROR',
                'error': 'ADB工具未找到'
            }
            print(f"  ❌ ADB工具未找到")
            print(f"  💡 解决方案: 请安装Android SDK Platform Tools并添加到系统PATH")
            
        except Exception as e:
            self.results['adb'] = {'status': 'ERROR', 'error': str(e)}
            print(f"  ❌ ADB检查失败: {e}")
    
    def _check_device_connection(self) -> None:
        """检查设备连接"""
        print("\n📱 检查设备连接...")
        
        try:
            result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                devices_output = result.stdout
                lines = devices_output.strip().split('\n')
                
                devices = []
                for line in lines[1:]:  # 跳过标题行
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            device_id = parts[0]
                            status = parts[1]
                            devices.append({'id': device_id, 'status': status})
                
                self.results['devices'] = {
                    'count': len(devices),
                    'devices': devices,
                    'status': 'OK' if devices else 'NO_DEVICES'
                }
                
                if devices:
                    print(f"  ✅ 检测到 {len(devices)} 个设备:")
                    for device in devices:
                        status_icon = "✅" if device['status'] == 'device' else "⚠️"
                        print(f"    {status_icon} {device['id']} - {device['status']}")
                else:
                    print(f"  ⚠️  未检测到设备")
                    print(f"  💡 请检查设备连接和USB调试设置")
            else:
                self.results['devices'] = {
                    'status': 'ERROR',
                    'error': result.stderr
                }
                print(f"  ❌ 设备检查失败: {result.stderr}")
                
        except Exception as e:
            self.results['devices'] = {'status': 'ERROR', 'error': str(e)}
            print(f"  ❌ 设备连接检查失败: {e}")
    
    def _check_dependencies(self) -> None:
        """检查依赖包"""
        print("\n📦 检查依赖包...")
        
        required_packages = ['uiautomator2']
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"  ✅ {package} - 已安装")
                self.results[f'package_{package}'] = {'status': 'OK'}
            except ImportError:
                print(f"  ❌ {package} - 未安装")
                print(f"  💡 请运行: pip install {package}")
                self.results[f'package_{package}'] = {'status': 'MISSING'}
    
    def _check_file_permissions(self) -> None:
        """检查文件权限和路径"""
        print("\n📁 检查文件权限和路径...")

        try:
            # 检查项目结构
            project_dirs = {
                'assets': 'assets',
                'assets/images': os.path.join('assets', 'images'),
                'assets/apk': os.path.join('assets', 'apk'),
                'output': 'output',
                'output/logs': os.path.join('output', 'logs'),
                'output/results': os.path.join('output', 'results'),
                'config': 'config'
            }

            missing_dirs = []
            permission_issues = []

            for name, dir_path in project_dirs.items():
                if os.path.exists(dir_path):
                    if os.access(dir_path, os.W_OK):
                        print(f"  ✅ {name} - 存在且可写")
                    else:
                        print(f"  ⚠️  {name} - 存在但无写入权限")
                        permission_issues.append(name)
                else:
                    print(f"  ❌ {name} - 目录不存在")
                    missing_dirs.append(name)

            # 检查关键文件
            key_files = {
                'ATX.apk': os.path.join('assets', 'apk', 'ATX.apk'),
                'default.json': os.path.join('config', 'default.json'),
                'device_profiles.json': os.path.join('config', 'device_profiles.json')
            }

            missing_files = []
            for name, file_path in key_files.items():
                if os.path.exists(file_path):
                    print(f"  ✅ {name} - 存在")
                else:
                    print(f"  ❌ {name} - 文件不存在: {file_path}")
                    missing_files.append(name)

            # 检查灰阶图片
            images_dir = os.path.join('assets', 'images')
            if os.path.exists(images_dir):
                image_files = [f for f in os.listdir(images_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
                if image_files:
                    print(f"  ✅ 灰阶图片 - 找到 {len(image_files)} 个文件")
                else:
                    print(f"  ⚠️  灰阶图片 - 目录存在但无图片文件")
                    missing_files.append('grey_images')

            # 设置结果状态
            if missing_dirs or missing_files or permission_issues:
                self.results['file_permissions'] = {
                    'status': 'WARNING',
                    'missing_dirs': missing_dirs,
                    'missing_files': missing_files,
                    'permission_issues': permission_issues
                }
            else:
                self.results['file_permissions'] = {'status': 'OK'}

        except Exception as e:
            self.results['file_permissions'] = {'status': 'ERROR', 'error': str(e)}
            print(f"  ❌ 文件权限检查失败: {e}")
    
    def _generate_report(self) -> None:
        """生成诊断报告"""
        print("\n" + "=" * 50)
        print("📊 诊断报告总结")
        print("=" * 50)
        
        # 统计问题
        errors = []
        warnings = []
        
        for key, value in self.results.items():
            if isinstance(value, dict):
                status = value.get('status', 'UNKNOWN')
                if status == 'ERROR':
                    errors.append(key)
                elif status in ['NO_DEVICES', 'MISSING']:
                    warnings.append(key)
        
        if not errors and not warnings:
            print("🎉 所有检查都通过了！系统状态良好。")
        else:
            if errors:
                print(f"❌ 发现 {len(errors)} 个错误:")
                for error in errors:
                    print(f"  - {error}")
            
            if warnings:
                print(f"⚠️  发现 {len(warnings)} 个警告:")
                for warning in warnings:
                    print(f"  - {warning}")
            
            print("\n💡 建议的解决步骤:")
            
            if 'adb' in errors:
                print("1. 安装Android SDK Platform Tools")
                print("2. 将ADB工具路径添加到系统PATH环境变量")
            
            if 'devices' in warnings or self.results.get('devices', {}).get('status') == 'NO_DEVICES':
                print("3. 检查Android设备连接:")
                print("   - 确保USB线连接正常")
                print("   - 在设备上开启USB调试模式")
                print("   - 允许此电脑的调试权限")
            
            for key in self.results:
                if key.startswith('package_') and self.results[key].get('status') == 'MISSING':
                    package_name = key.replace('package_', '')
                    print(f"4. 安装缺失的依赖包: pip install {package_name}")


def run_diagnostic():
    """运行诊断工具的便捷函数"""
    diagnostic = SystemDiagnostic()
    return diagnostic.run_full_diagnostic()


if __name__ == "__main__":
    run_diagnostic()
