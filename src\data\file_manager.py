"""
文件管理模块
负责测试结果的保存和格式化
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from ..core.adb_controller import ADBController
from ..utils.constants import RESULTS_DIR
from ..utils.logger import get_logger


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        self.adb = ADBController()
        self.logger = get_logger()
        self.device_info = {}
        self._load_device_info()
    
    def _load_device_info(self) -> None:
        """加载设备信息"""
        try:
            self.device_info = self.adb.get_device_info()
        except Exception as e:
            if self.logger:
                self.logger.warning(f"获取设备信息失败: {e}")
            self.device_info = {
                'device_name': 'Unknown',
                'software_version': 'Unknown',
                'android_version': 'Unknown'
            }
    
    def save_test_results(self, 
                         test_data: List[Dict[str, Any]], 
                         test_type: str,
                         brightness_data: Optional[List[Dict]] = None,
                         additional_info: Optional[Dict] = None) -> str:
        """
        保存测试结果
        
        Args:
            test_data: 测试数据
            test_type: 测试类型 (LTPO/LTPS)
            brightness_data: 亮度数据
            additional_info: 额外信息
            
        Returns:
            保存的文件路径
        """
        try:
            # 使用绝对路径确保跨平台兼容性
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            results_dir = os.path.join(project_root, RESULTS_DIR)

            # 确保输出目录存在
            os.makedirs(results_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime('%Y-%m-%d %H-%M-%S')
            device_name = self.device_info.get('device_name', 'Unknown').replace('\n', '').strip()
            # 清理设备名称中的非法字符
            device_name = "".join(c for c in device_name if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"{device_name}_{timestamp}_out_result.txt"
            filepath = os.path.join(results_dir, filename)
            
            # 格式化并保存数据
            formatted_content = self._format_test_results(
                test_data, test_type, brightness_data, additional_info
            )
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(formatted_content)
            
            if self.logger:
                self.logger.info(f"测试结果已保存到: {filepath}")
            
            return filepath
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"保存测试结果失败: {e}")
            return ""
    
    def _format_test_results(self, 
                           test_data: List[Dict[str, Any]], 
                           test_type: str,
                           brightness_data: Optional[List[Dict]] = None,
                           additional_info: Optional[Dict] = None) -> str:
        """
        格式化测试结果
        
        Args:
            test_data: 测试数据
            test_type: 测试类型
            brightness_data: 亮度数据
            additional_info: 额外信息
            
        Returns:
            格式化后的字符串
        """
        content_lines = []
        
        # 添加头部信息
        content_lines.extend(self._format_header(test_type, additional_info))
        
        # 添加测试结果
        content_lines.append("\n测试结果:\n")
        content_lines.extend(self._format_main_results(test_data, test_type))
        
        # 添加亮度门限数据
        if brightness_data:
            content_lines.append("\n亮度门限:\n")
            content_lines.extend(self._format_brightness_data(brightness_data))
        
        # 添加原始数据
        content_lines.append("\n原始数据:\n")
        content_lines.extend(self._format_raw_data(test_data))
        
        return '\n'.join(content_lines)
    
    def _format_header(self, test_type: str, additional_info: Optional[Dict] = None) -> List[str]:
        """格式化头部信息"""
        lines = []
        
        # 设备信息
        device_name = self.device_info.get('device_name', 'Unknown').replace('\n', '').strip()
        software_version = self.device_info.get('software_version', 'Unknown').replace('\n', '').strip()
        
        lines.append(f"设备名称: {device_name}")
        lines.append(f"软件版本: {software_version}")
        
        # 深色模式状态
        try:
            dark_mode_status = self.adb.get_dark_mode_status()
            if dark_mode_status and "yes" in dark_mode_status.lower():
                dark_mode_text = "开启"
            else:
                dark_mode_text = "关闭"
        except Exception:
            dark_mode_text = "状态未知"
        
        lines.append(f"深色模式: {dark_mode_text}")
        lines.append(f"{test_type}灰阶测试结果")
        
        # 测试时间
        test_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        lines.append(f"测试时间: {test_time}")
        
        # 额外信息
        if additional_info:
            for key, value in additional_info.items():
                lines.append(f"{key}: {value}")
        
        return lines
    
    def _format_main_results(self, test_data: List[Dict[str, Any]], test_type: str) -> List[str]:
        """格式化主要测试结果"""
        lines = []
        
        if not test_data:
            lines.append("无测试数据")
            return lines
        
        try:
            if test_type.upper() == "LTPS":
                lines.extend(self._format_ltps_results(test_data))
            else:
                lines.extend(self._format_ltpo_results(test_data))
        except Exception as e:
            if self.logger:
                self.logger.error(f"格式化主要结果失败: {e}")
            lines.append(f"格式化结果失败: {e}")
        
        return lines
    
    def _format_ltps_results(self, test_data: List[Dict[str, Any]]) -> List[str]:
        """格式化LTPS测试结果"""
        lines = []
        
        # 按NIT值分组
        nit_groups = {}
        for item in test_data:
            nit = item.get('nit', 0)
            if nit not in nit_groups:
                nit_groups[nit] = []
            nit_groups[nit].append(item)
        
        # 排序并格式化
        sorted_nits = sorted(nit_groups.keys())
        
        for i, nit in enumerate(sorted_nits):
            items = nit_groups[nit]
            fps = items[0].get('fps', 0)
            grey_values = [item.get('grey_value', item.get('index', 0)) for item in items]
            grey_str = '-'.join(map(str, sorted(set(grey_values))))
            
            if i == 0:
                lines.append(f"设置亮度/nit: 0-{nit}nit")
            elif i == len(sorted_nits) - 1:
                lines.append(f"设置亮度/nit: >{nit}nit")
            else:
                prev_nit = sorted_nits[i-1]
                lines.append(f"设置亮度/nit: {prev_nit}-{nit}nit")
            
            lines.append(f"刷新率/fps: {fps}fps")
            lines.append(f"灰阶值/Grey: {grey_str}")
            lines.append("")
        
        return lines
    
    def _format_ltpo_results(self, test_data: List[Dict[str, Any]]) -> List[str]:
        """格式化LTPO测试结果"""
        lines = []
        
        # 简化的LTPO结果格式化
        for item in test_data:
            nit = item.get('nit', 0)
            fps = item.get('fps', 0)
            brightness = item.get('brightness', 0)
            grey_value = item.get('grey_value', item.get('index', 0))
            
            lines.append(f"亮度: {brightness} -> NIT: {nit} -> FPS: {fps} -> 灰阶: {grey_value}")
        
        return lines
    
    def _format_brightness_data(self, brightness_data: List[Dict]) -> List[str]:
        """格式化亮度数据"""
        lines = []
        
        try:
            # 按NIT值排序
            sorted_data = sorted(
                brightness_data,
                key=lambda x: int(str(x.get('nit', 0))) if str(x.get('nit', 0)).isdigit() else 0
            )
            
            for item in sorted_data:
                lines.append(str(item))
        
        except Exception as e:
            if self.logger:
                self.logger.error(f"格式化亮度数据失败: {e}")
            lines.append(f"格式化亮度数据失败: {e}")
        
        return lines
    
    def _format_raw_data(self, test_data: List[Dict[str, Any]]) -> List[str]:
        """格式化原始数据"""
        lines = []
        
        lines.append("参数说明:")
        lines.append("- nit: 亮度值")
        lines.append("- fps: 帧率")
        lines.append("- brightness: 背光值")
        lines.append("- grey_value: 灰阶值")
        lines.append("- index: 灰阶图片索引")
        lines.append("")
        
        for item in test_data:
            lines.append(str(item))
        
        return lines
    
    def save_json_results(self, data: Dict[str, Any], filename: str) -> str:
        """
        保存JSON格式的结果

        Args:
            data: 要保存的数据
            filename: 文件名

        Returns:
            保存的文件路径
        """
        try:
            # 使用绝对路径确保跨平台兼容性
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            results_dir = os.path.join(project_root, RESULTS_DIR)

            os.makedirs(results_dir, exist_ok=True)
            filepath = os.path.join(results_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)

            if self.logger:
                self.logger.info(f"JSON结果已保存到: {filepath}")

            return filepath

        except Exception as e:
            if self.logger:
                self.logger.error(f"保存JSON结果失败: {e}")
            return ""
    
    def load_test_results(self, filepath: str) -> Optional[str]:
        """
        加载测试结果文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            文件内容
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            if self.logger:
                self.logger.error(f"加载测试结果失败: {e}")
            return None
    
    def list_result_files(self) -> List[str]:
        """
        列出所有结果文件

        Returns:
            结果文件列表
        """
        try:
            # 使用绝对路径确保跨平台兼容性
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            results_dir = os.path.join(project_root, RESULTS_DIR)

            if not os.path.exists(results_dir):
                return []

            files = []
            for filename in os.listdir(results_dir):
                if filename.endswith('.txt') or filename.endswith('.json'):
                    files.append(os.path.join(results_dir, filename))

            return sorted(files, key=os.path.getmtime, reverse=True)

        except Exception as e:
            if self.logger:
                self.logger.error(f"列出结果文件失败: {e}")
            return []
