"""
常量定义模块
定义项目中使用的所有常量
"""

# 文件路径常量
import os

ASSETS_DIR = "assets"
IMAGES_DIR = os.path.join(ASSETS_DIR, "images")
APK_DIR = os.path.join(ASSETS_DIR, "apk")
OUTPUT_DIR = "output"
RESULTS_DIR = os.path.join(OUTPUT_DIR, "results")
LOGS_DIR = os.path.join(OUTPUT_DIR, "logs")
CONFIG_DIR = "config"

# APK文件名
ATX_APK = "ATX.apk"

# 设备相关常量
VIVO_GALLERY_PACKAGE = "com.vivo.gallery"
UIAUTOMATOR_PACKAGE = "uiautomator"

# 灰阶图片相关常量
GREY_IMAGE_PREFIX = "P3_gray"
GREY_IMAGE_SUFFIX = "jpg"
MAX_GREY_INDEX = 64  # 白色
MIN_GREY_INDEX = 0   # 黑色

# 亮度相关常量
MIN_BRIGHTNESS = 1
MAX_BRIGHTNESS = 255

# 帧率相关常量
SMART_FPS_MODE = 1
STANDARD_FPS_MODE = 60
HIGH_FPS_MODE = 120

# ADB命令相关常量
ADB_COMMANDS = {
    'devices': 'adb devices',
    'root': 'adb root',
    'vivo_root': 'adb vivoroot',
    'wait_device': 'adb wait-for-device',
    'wake_screen': 'adb shell input keyevent KEYCODE_WAKEUP',
    'unlock_screen': 'adb shell input keyevent KEYCODE_MENU',
    'home_key': 'adb shell input keyevent KEYCODE_HOME',
    'get_screen_lock_status': 'adb shell dumpsys power | grep "mWakefulness"',
    'auto_brightness_on': 'adb shell settings put system screen_brightness_mode 1',
    'auto_brightness_off': 'adb shell settings put system screen_brightness_mode 0',
    'get_auto_brightness': 'adb shell settings get system screen_brightness_mode',
    'dark_mode_on': 'adb shell cmd uimode night yes',
    'dark_mode_off': 'adb shell cmd uimode night no',
    'get_dark_mode': 'adb shell cmd uimode night',
    'set_fps_smart': 'adb shell settings put global vivo_screen_refresh_rate_mode 1',
    'set_fps_60': 'adb shell settings put global vivo_screen_refresh_rate_mode 60',
    'set_fps_120': 'adb shell settings put global vivo_screen_refresh_rate_mode 120',
    'stay_awake_on': 'adb shell settings put global stay_on_while_plugged_in 7',
    'stay_awake_off': 'adb shell settings put global stay_on_while_plugged_in 0',
    'get_stay_awake': 'adb shell settings get global stay_on_while_plugged_in',
}

# 日志相关常量
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'

# 测试相关常量
TEST_TYPES = {
    'LTPO': 'ltpo',
    'LTPS': 'ltps'
}

# 错误消息
ERROR_MESSAGES = {
    'NO_DEVICE': '无法连接设备，请确认设备状态...',
    'NO_ROOT': '无法获取root权限',
    'NO_GREY_STRATEGY': '该设备不存在灰阶策略',
    'NO_NIT_VALUE': '无法获取到亮度值，请确认设备状态...',
    'ATX_INSTALL_FAILED': 'ATX安装失败',
}

# 成功消息
SUCCESS_MESSAGES = {
    'ATX_INSTALLED': 'ATX安装成功',
    'ROOT_OBTAINED': 'root权限获取成功',
    'TEST_COMPLETED': '测试完成',
    'FILE_SAVED': '文件保存成功',
}

# 默认配置值
DEFAULT_CONFIG = {
    'test_timeout': 300,  # 测试超时时间（秒）
    'retry_count': 3,     # 重试次数
    'wait_time': 2,       # 默认等待时间（秒）
    'fps_check_count': 5, # FPS检查次数
    'remote_path': '/sdcard/',  # 设备存储路径（Android路径，始终使用/）
}
