# API 文档

## 核心模块

### ADBController

ADB控制器，提供Android设备的基本操作功能。

#### 主要方法

```python
class ADBController:
    def execute_command(self, command: str, capture_output: bool = False) -> Optional[str]
    def check_device_connected(self) -> bool
    def get_root_permission(self) -> bool
    def set_brightness(self, brightness: int) -> None
    def get_auto_brightness_status(self) -> bool
    def set_dark_mode(self, enabled: bool) -> None
    def set_fps_mode(self, fps_mode: int) -> None
    def upload_file(self, local_path: str, remote_path: str) -> bool
    def install_apk(self, apk_path: str) -> bool
```

### DeviceManager

设备管理器，负责设备的初始化和状态管理。

#### 主要方法

```python
class DeviceManager:
    def initialize_device(self) -> bool
    def get_device_info(self) -> Dict[str, Any]
    def set_brightness(self, brightness: int) -> None
    def set_dark_mode(self, enabled: bool) -> None
    def set_fps_mode(self, fps_mode: int) -> None
    def cleanup(self) -> None
```

### UIAutomation

UI自动化控制器，提供设备UI操作功能。

#### 主要方法

```python
class UIAutomation:
    def connect(self) -> bool
    def click_text(self, text: str, timeout: int = 10) -> bool
    def click_xpath(self, xpath: str, timeout: int = 10) -> bool
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5) -> bool
    def swipe_left(self, times: int = 1, speed: float = 0.5) -> bool
```

### GreyScaleImageController

灰阶图片控制器，专门处理灰阶图片的打开和操作。

#### 主要方法

```python
class GreyScaleImageController:
    def open_grey_image(self, index: int) -> bool
```

## 测试模块

### BaseTest

基础测试类，提供测试的通用功能。

#### 主要方法

```python
class BaseTest(ABC):
    def initialize(self) -> bool
    def cleanup(self) -> None
    def check_grey_scale_strategy(self) -> bool
    def execute_test(self) -> bool
    def save_results(self, additional_info: Optional[Dict] = None) -> str
    
    @abstractmethod
    def run_test(self) -> bool
    
    @abstractmethod
    def get_test_type(self) -> str
```

### LTPoTest

LTPO测试类，继承自BaseTest。

#### 主要方法

```python
class LTPoTest(BaseTest):
    def test_brightness_gradient(self) -> List[Dict[str, Any]]
    def test_grey_scale_threshold(self) -> List[Dict[str, Any]]
    def run_test(self) -> bool
```

### LTpsTest

LTPS测试类，继承自BaseTest。

#### 主要方法

```python
class LTpsTest(BaseTest):
    def test_brightness_threshold(self, image_type: str = "BLACK") -> List[Dict[str, Any]]
    def test_grey_scale_threshold(self, brightness_range: Tuple[int, int]) -> List[Dict[str, Any]]
    def run_test(self) -> bool
```

## 数据处理模块

### DataCollectionManager

数据收集管理器，负责收集测试数据。

#### 主要方法

```python
class DataCollectionManager:
    def start_collection(self) -> None
    def stop_collection(self) -> None
    def get_current_data(self) -> Dict[str, Any]
    def collect_brightness_data(self, brightness: int) -> Dict[str, Any]
```

### DataProcessor

数据处理器，负责数据的处理和分析。

#### 主要方法

```python
class DataProcessor:
    def merge_brightness_grey_data(self, brightness_data: List[Dict], grey_data: List[Dict]) -> List[Dict]
    def sort_data_by_nit(self, data: List[Dict], reverse: bool = False) -> List[Dict]
    def filter_data_by_fps(self, data: List[Dict], target_fps: int) -> List[Dict]
    def validate_data(self, data: List[Dict]) -> Tuple[bool, List[str]]
```

### FileManager

文件管理器，负责结果的保存和管理。

#### 主要方法

```python
class FileManager:
    def save_test_results(self, test_data: List[Dict[str, Any]], test_type: str, 
                         brightness_data: Optional[List[Dict]] = None,
                         additional_info: Optional[Dict] = None) -> str
    def save_json_results(self, data: Dict[str, Any], filename: str) -> str
    def load_test_results(self, filepath: str) -> Optional[str]
    def list_result_files(self) -> List[str]
```

## 工具模块

### ConfigManager

配置管理器，负责配置的加载和管理。

#### 主要方法

```python
class ConfigManager:
    def load_config(self) -> None
    def save_config(self) -> None
    def get(self, key: str, default: Any = None) -> Any
    def set(self, key: str, value: Any) -> None
    def update(self, config_dict: Dict[str, Any]) -> None
```

### LoggerManager

日志管理器，提供统一的日志功能。

#### 主要方法

```python
class LoggerManager:
    def setup_logging(self, log_level: str = LOG_LEVEL) -> str
    def get_logger(self) -> Optional[logging.Logger]
    def info(self, message: str) -> None
    def warning(self, message: str) -> None
    def error(self, message: str, exc_info: bool = False) -> None
```

## 使用示例

### 基本使用

```python
from src.test_logic.ltpo_test import run_ltpo_test
from src.test_logic.ltps_test import run_ltps_test

# 运行LTPO测试
success = run_ltpo_test(desktop_test=False, enable_inspection=True)

# 运行LTPS测试
success = run_ltps_test(desktop_test=True, enable_inspection=False)
```

### 自定义测试

```python
from src.test_logic.base_test import BaseTest

class CustomTest(BaseTest):
    def get_test_type(self) -> str:
        return "CUSTOM"
    
    def run_test(self) -> bool:
        # 实现自定义测试逻辑
        return True

# 运行自定义测试
test = CustomTest()
success = test.execute_test()
```

### 配置管理

```python
from src.utils.config import config_manager

# 获取配置
timeout = config_manager.get('test_timeout', 300)

# 设置配置
config_manager.set('test_timeout', 600)
config_manager.save_config()
```

### 数据处理

```python
from src.data.data_processor import DataProcessor

processor = DataProcessor()

# 合并数据
merged_data = processor.merge_brightness_grey_data(brightness_data, grey_data)

# 验证数据
is_valid, errors = processor.validate_data(merged_data)

# 排序数据
sorted_data = processor.sort_data_by_nit(merged_data)
```
