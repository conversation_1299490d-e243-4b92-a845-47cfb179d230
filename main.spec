# -*- mode: python ; coding: utf-8 -*-
import sys
import os
from os.path import dirname

# 显式导入uiautomator2模块
import uiautomator2

u2_path = dirname(uiautomator2.__file__)

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['src/main.py'],
    pathex=[project_root],
    binaries=[],
    datas=[
        # uiautomator2相关文件
        (f'{u2_path}/assets/*', 'uiautomator2/assets'),
        (f'{u2_path}/*', 'uiautomator2'),
        # 项目配置文件
        (os.path.join(project_root, 'config'), 'config'),
        # 项目资源文件
        (os.path.join(project_root, 'assets'), 'assets'),
    ],
    hiddenimports=[
        'src.core.adb_controller',
        'src.core.device_manager',
        'src.core.ui_automation',
        'src.test_logic.base_test',
        'src.test_logic.ltpo_test',
        'src.test_logic.ltps_test',
        'src.data.log_collector',
        'src.data.data_processor',
        'src.data.file_manager',
        'src.utils.config',
        'src.utils.logger',
        'src.utils.constants',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
