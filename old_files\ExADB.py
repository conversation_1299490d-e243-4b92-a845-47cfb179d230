import time

from ADB import ADB
from U2 import U2


class ExADB:

    def __init__(self, desktop_test):
        self.adb = ADB()
        self.u2 = U2()
        # 是否使用桌面壁纸进行测试
        self.desktop_test = desktop_test

    def open_system_grey_image(self, index: int, file_suffix="jpg"):
        """
        使用系统相册打开指定灰阶图片
        MAX 64 白
        MIN 00 黑
        """
        # self.close_system_image()
        # 替换更通用的关闭方式
        self.adb.stop_current_app()
        if index >= 10:
            file_name = 'P3_gray' + str(index)
        else:
            file_name = 'P3_gray0' + str(index)
        time.sleep(1)
        if self.desktop_test:
            # 使用ATX 操作切换壁纸
            self.adb.user_run_adb("adb shell am start -a android.intent.action.ATTACH_DATA -d file:///sdcard/static_grey/" + file_name + "." + file_suffix + " -t image/jpeg")
            time.sleep(2)
            if self.u2.is_element_text_exist("设为默认选项。"):
                self.u2.xpath_click('//*[@resource-id="com.android.intentresolver:id/resolver_button_always"]')
                time.sleep(1)
                self.u2.text_click("壁纸")
            if self.u2.is_element_text_exist("始终"):
                self.u2.text_click("壁纸")
                time.sleep(1)
                self.u2.text_click("始终")
            if self.u2.is_element_text_exist("设置壁纸"):
                self.u2.text_click("设置壁纸")
            time.sleep(2)
            if self.u2.is_element_text_exist("确定"):
                self.u2.text_click("确定")
            else:
                self.u2.xpath_click('//*[@resource-id="com.vivo.gallery:id/vivo_title_view"]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]')
            time.sleep(2)
        else:
            self.adb.user_run_adb(
                'adb shell am start -a android.intent.action.VIEW -n com.vivo.gallery/com.android.gallery3d.app.Gallery -d "file:///sdcard/static_grey/' + file_name + '.' + file_suffix + '" -t "image/*"')
            time.sleep(1)
            self.adb.click_double()
