"""
UIAutomator2问题诊断和修复工具
用于解决UIAutomator2服务冲突和连接问题
"""

import subprocess
import time
import os
from typing import List, Dict, Any
from .logger import get_logger


class UIAutomator2Fixer:
    """UIAutomator2修复工具"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def diagnose_and_fix(self) -> Dict[str, Any]:
        """
        诊断并修复UIAutomator2问题
        
        Returns:
            诊断和修复结果
        """
        result = {
            'diagnosis': {},
            'fixes_applied': [],
            'success': False,
            'message': ''
        }
        
        try:
            if self.logger:
                self.logger.info("开始UIAutomator2问题诊断...")
            
            # 1. 检查设备连接
            if not self._check_device_connection():
                result['message'] = '设备未连接'
                return result
            
            # 2. 检查UIAutomator2服务状态
            service_status = self._check_uiautomator_service()
            result['diagnosis']['service_status'] = service_status
            
            # 3. 检查进程冲突
            process_conflicts = self._check_process_conflicts()
            result['diagnosis']['process_conflicts'] = process_conflicts
            
            # 4. 检查accessibility服务
            accessibility_status = self._check_accessibility_service()
            result['diagnosis']['accessibility_status'] = accessibility_status
            
            # 5. 应用修复措施
            fixes = self._apply_fixes(service_status, process_conflicts, accessibility_status)
            result['fixes_applied'] = fixes
            
            # 6. 验证修复效果
            if self._verify_fix():
                result['success'] = True
                result['message'] = 'UIAutomator2问题已修复'
            else:
                result['success'] = False
                result['message'] = 'UIAutomator2问题修复失败，建议重启设备'
            
            return result
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"UIAutomator2诊断修复失败: {e}")
            result['message'] = f'诊断修复过程出错: {e}'
            return result
    
    def _check_device_connection(self) -> bool:
        """检查设备连接"""
        try:
            result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:
                    if line.strip() and '\tdevice' in line:
                        return True
            return False
        except Exception:
            return False
    
    def _check_uiautomator_service(self) -> Dict[str, Any]:
        """检查UIAutomator2服务状态"""
        status = {
            'app_installed': False,
            'test_installed': False,
            'service_running': False,
            'port_occupied': False
        }
        
        try:
            # 检查UIAutomator2应用是否安装
            result = subprocess.run(
                "adb shell pm list packages | grep com.github.uiautomator",
                shell=True, capture_output=True, text=True
            )
            if "com.github.uiautomator" in result.stdout:
                status['app_installed'] = True
            
            if "com.github.uiautomator.test" in result.stdout:
                status['test_installed'] = True
            
            # 检查服务是否运行
            result = subprocess.run(
                "adb shell ps | grep uiautomator",
                shell=True, capture_output=True, text=True
            )
            if "uiautomator" in result.stdout:
                status['service_running'] = True
            
            # 检查端口是否被占用
            result = subprocess.run(
                "adb shell netstat -an | grep 9008",
                shell=True, capture_output=True, text=True
            )
            if "9008" in result.stdout:
                status['port_occupied'] = True
            
        except Exception as e:
            if self.logger:
                self.logger.debug(f"检查UIAutomator2服务状态失败: {e}")
        
        return status
    
    def _check_process_conflicts(self) -> List[str]:
        """检查进程冲突"""
        conflicts = []
        
        try:
            # 检查是否有多个UIAutomator进程
            result = subprocess.run(
                "adb shell ps | grep -i uiautomator",
                shell=True, capture_output=True, text=True
            )
            
            lines = result.stdout.strip().split('\n')
            uiautomator_processes = [line for line in lines if line.strip() and 'uiautomator' in line.lower()]
            
            if len(uiautomator_processes) > 1:
                conflicts.append(f"检测到{len(uiautomator_processes)}个UIAutomator进程")
            
            # 检查accessibility服务冲突
            result = subprocess.run(
                "adb shell dumpsys accessibility | grep -i uiautomator",
                shell=True, capture_output=True, text=True
            )
            
            if "UiAutomationService" in result.stdout and "already registered" in result.stdout:
                conflicts.append("UIAutomationService已注册冲突")
            
        except Exception as e:
            if self.logger:
                self.logger.debug(f"检查进程冲突失败: {e}")
        
        return conflicts
    
    def _check_accessibility_service(self) -> Dict[str, Any]:
        """检查accessibility服务状态"""
        status = {
            'enabled_services': [],
            'uiautomator_enabled': False,
            'service_conflicts': False
        }
        
        try:
            # 获取已启用的accessibility服务
            result = subprocess.run(
                "adb shell settings get secure enabled_accessibility_services",
                shell=True, capture_output=True, text=True
            )
            
            if result.stdout.strip():
                services = result.stdout.strip().split(':')
                status['enabled_services'] = services
                
                # 检查UIAutomator是否在其中
                for service in services:
                    if 'uiautomator' in service.lower():
                        status['uiautomator_enabled'] = True
                        break
                
                # 检查是否有服务冲突
                if len(services) > 1:
                    status['service_conflicts'] = True
            
        except Exception as e:
            if self.logger:
                self.logger.debug(f"检查accessibility服务失败: {e}")
        
        return status
    
    def _apply_fixes(self, service_status: Dict, conflicts: List, accessibility_status: Dict) -> List[str]:
        """应用修复措施"""
        fixes = []
        
        try:
            # 1. 强制停止所有UIAutomator相关进程
            if service_status.get('service_running') or conflicts:
                self._execute_command("adb shell pkill -9 -f uiautomator")
                self._execute_command("adb shell am force-stop com.github.uiautomator")
                self._execute_command("adb shell am force-stop com.github.uiautomator.test")
                fixes.append("强制停止UIAutomator进程")
                time.sleep(2)
            
            # 2. 清理accessibility服务注册
            if accessibility_status.get('service_conflicts') or accessibility_status.get('uiautomator_enabled'):
                self._execute_command("adb shell settings put secure enabled_accessibility_services ''")
                fixes.append("清理accessibility服务注册")
                time.sleep(1)
            
            # 3. 重启accessibility服务
            self._execute_command("adb shell service call accessibility 1")
            fixes.append("重启accessibility服务")
            time.sleep(2)
            
            # 4. 如果UIAutomator2未安装，尝试重新安装
            if not service_status.get('app_installed') or not service_status.get('test_installed'):
                # 这里可以添加重新安装UIAutomator2的逻辑
                fixes.append("检测到UIAutomator2未完整安装")
            
            # 5. 清理端口占用
            if service_status.get('port_occupied'):
                self._execute_command("adb shell netstat -tlnp | grep :9008 | awk '{print $7}' | cut -d'/' -f1 | xargs kill -9")
                fixes.append("清理端口占用")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"应用修复措施失败: {e}")
        
        return fixes
    
    def _execute_command(self, command: str) -> bool:
        """执行命令"""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False
    
    def _verify_fix(self) -> bool:
        """验证修复效果"""
        try:
            # 等待服务稳定
            time.sleep(3)
            
            # 尝试连接UIAutomator2
            import uiautomator2 as u2
            device = u2.connect()
            
            # 简单验证连接
            info = device.info
            if info and 'displayWidth' in info:
                return True
            
            return False
            
        except Exception as e:
            if self.logger:
                self.logger.debug(f"验证修复效果失败: {e}")
            return False


def fix_uiautomator2_issues() -> Dict[str, Any]:
    """修复UIAutomator2问题的便捷函数"""
    fixer = UIAutomator2Fixer()
    return fixer.diagnose_and_fix()


def print_fix_result(result: Dict[str, Any]) -> None:
    """打印修复结果"""
    print("\n🔧 UIAutomator2问题诊断和修复结果")
    print("=" * 50)
    
    # 打印诊断结果
    if 'diagnosis' in result:
        print("📋 诊断结果:")
        diagnosis = result['diagnosis']
        
        if 'service_status' in diagnosis:
            status = diagnosis['service_status']
            print(f"  - 应用安装状态: {'✅' if status.get('app_installed') else '❌'}")
            print(f"  - 测试包安装状态: {'✅' if status.get('test_installed') else '❌'}")
            print(f"  - 服务运行状态: {'✅' if status.get('service_running') else '❌'}")
            print(f"  - 端口占用状态: {'⚠️' if status.get('port_occupied') else '✅'}")
        
        if 'process_conflicts' in diagnosis:
            conflicts = diagnosis['process_conflicts']
            if conflicts:
                print(f"  - 进程冲突: {', '.join(conflicts)}")
            else:
                print("  - 进程冲突: 无")
    
    # 打印修复措施
    if 'fixes_applied' in result and result['fixes_applied']:
        print("\n🛠️ 已应用的修复措施:")
        for fix in result['fixes_applied']:
            print(f"  - {fix}")
    
    # 打印最终结果
    print(f"\n📊 修复结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
    print(f"💬 说明: {result.get('message', '无')}")


if __name__ == "__main__":
    result = fix_uiautomator2_issues()
    print_fix_result(result)
