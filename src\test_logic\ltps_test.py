"""
LTPS测试逻辑模块
实现LTPS设备的灰阶测试功能
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from .base_test import BaseTest
from ..utils.constants import MIN_BRIGHTNESS, MAX_BRIGHTNESS
from ..utils.logger import get_logger


class LTpsTest(BaseTest):
    """LTPS测试类"""
    
    def __init__(self, desktop_test: bool = False, enable_inspection: bool = True):
        """
        初始化LTPS测试
        
        Args:
            desktop_test: 是否为桌面测试
            enable_inspection: 是否启用灰阶校验
        """
        super().__init__(desktop_test, enable_inspection)
        self.test_type = "LTPS"
        self.threshold_data_black = []
    
    def get_test_type(self) -> str:
        """获取测试类型"""
        return self.test_type
    
    def test_brightness_threshold(self) -> List[Dict[str, Any]]:
        """
        测试亮度门限（仅使用黑色图片）
        
        Returns:
            亮度门限数据列表
        """
        try:
            if self.logger:
                self.logger.info("开始黑色图片亮度门限测试...")
            
            # 使用黑色灰阶图片（索引0）
            grey_index = 0
            
            # 打开灰阶图片
            self.image_controller.open_grey_image(grey_index)
            
            # 设置最低亮度并等待稳定
            self.device_manager.set_brightness(MIN_BRIGHTNESS)
            time.sleep(self.wait_time)
            self.device_manager.reset_screen_policy()
            time.sleep(5)
            
            # 获取低亮度时的FPS
            low_brightness_fps = self.wait_for_stable_fps()
            
            if self.logger:
                self.logger.info(f"低亮度FPS: {low_brightness_fps}")
            
            # 设置最高亮度并等待稳定
            self.device_manager.set_brightness(MAX_BRIGHTNESS)
            self.device_manager.reset_screen_policy()
            time.sleep(5)
            
            # 获取高亮度时的FPS
            high_brightness_fps = self.wait_for_stable_fps()
            
            if self.logger:
                self.logger.info(f"高亮度FPS: {high_brightness_fps}")
            
            # 如果FPS没有变化，说明没有亮度门限
            if low_brightness_fps == high_brightness_fps:
                if self.logger:
                    self.logger.warning("黑色图片没有检测到亮度门限")
                return []
            
            # 二分查找临界亮度
            threshold_brightness = self.binary_search_brightness_threshold(
                low=MIN_BRIGHTNESS,
                high=MAX_BRIGHTNESS,
                expected_fps=low_brightness_fps,
                target_fps=high_brightness_fps,
                grey_index=grey_index
            )
            
            if self.logger:
                self.logger.info(f"黑色图片亮度门限: {threshold_brightness}")
            
            # 收集门限点前后的数据
            threshold_data = []
            
            # 门限点前的数据
            self.device_manager.set_brightness(threshold_brightness - 1)
            self.device_manager.reset_screen_policy()
            time.sleep(5)
            
            before_data = self.data_collector.get_current_data()
            threshold_data.append({
                'nit': before_data.get('nit', 0),
                'fps': before_data.get('fps', 0),
                'brightness': threshold_brightness - 1,
                'image_type': 'BLACK'
            })
            
            # 门限点后的数据
            self.device_manager.set_brightness(threshold_brightness)
            self.device_manager.reset_screen_policy()
            time.sleep(5)
            
            after_data = self.data_collector.get_current_data()
            threshold_data.append({
                'nit': after_data.get('nit', 0),
                'fps': after_data.get('fps', 0),
                'brightness': threshold_brightness,
                'image_type': 'BLACK'
            })
            
            return threshold_data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"黑色图片亮度门限测试失败: {e}")
            return []
    
    def test_grey_scale_threshold(self, brightness_range: Tuple[int, int]) -> List[Dict[str, Any]]:
        """
        测试灰阶门限
        
        Args:
            brightness_range: 亮度范围 (最小值, 最大值)
            
        Returns:
            灰阶门限数据列表
        """
        try:
            if self.logger:
                self.logger.info("开始LTPS灰阶门限测试...")
            
            min_brightness, max_brightness = brightness_range
            grey_data = []
            
            # 在亮度范围内测试不同的灰阶值
            test_brightness_values = [
                min_brightness,
                (min_brightness + max_brightness) // 2,
                max_brightness
            ]
            
            for brightness in test_brightness_values:
                if self.logger:
                    self.logger.info(f"测试亮度 {brightness} 下的灰阶门限")
                
                # 设置亮度
                self.device_manager.set_brightness(brightness)
                time.sleep(self.wait_time)
                
                # 测试不同灰阶值
                for grey_index in range(0, 65, 8):  # 每8个灰阶测试一次
                    try:
                        # 打开灰阶图片
                        self.image_controller.open_grey_image(grey_index)
                        self.device_manager.reset_screen_policy()
                        time.sleep(3)
                        
                        # 获取当前数据
                        current_data = self.data_collector.get_current_data()
                        
                        grey_data.append({
                            'nit': current_data.get('nit', 0),
                            'fps': current_data.get('fps', 0),
                            'brightness': brightness,
                            'grey_value': grey_index,
                            'index': grey_index,
                            'range': brightness
                        })
                        
                        if self.logger:
                            self.logger.debug(f"灰阶 {grey_index}, 亮度 {brightness}: {current_data}")
                        
                    except Exception as e:
                        if self.logger:
                            self.logger.warning(f"测试灰阶 {grey_index} 失败: {e}")
                        continue
            
            return grey_data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"灰阶门限测试失败: {e}")
            return []
    
    def _get_brightness_range_from_threshold_data(self) -> Tuple[int, int]:
        """
        从门限数据中获取亮度范围
        
        Returns:
            (最小亮度, 最大亮度)
        """
        try:
            if not self.threshold_data_black:
                return MIN_BRIGHTNESS, MAX_BRIGHTNESS
            
            brightness_values = [item.get('brightness', 0) for item in self.threshold_data_black]
            return min(brightness_values), max(brightness_values)
            
        except Exception:
            return MIN_BRIGHTNESS, MAX_BRIGHTNESS
    
    def run_test(self) -> bool:
        """
        运行LTPS测试
        
        Returns:
            测试是否成功
        """
        try:
            if self.logger:
                self.logger.info("开始执行LTPS灰阶测试...")
            
            # 1. 测试黑色图片的亮度门限
            self.threshold_data_black = self.test_brightness_threshold()
            if self.threshold_data_black:
                if self.logger:
                    self.logger.info(f"黑色图片亮度门限测试完成，获得 {len(self.threshold_data_black)} 个数据点")
            
            # 2. 使用黑色图片的亮度门限数据
            self.brightness_data = self.threshold_data_black
            
            # 3. 测试灰阶门限
            brightness_range = self._get_brightness_range_from_threshold_data()
            self.grey_data = self.test_grey_scale_threshold(brightness_range)
            
            if self.grey_data:
                if self.logger:
                    self.logger.info(f"灰阶门限测试完成，获得 {len(self.grey_data)} 个数据点")
            
            # 4. 处理测试结果
            if self.grey_data:
                # 对于LTPS，直接使用灰阶数据作为主要结果
                self.test_results = self.grey_data
                
                # 使用数据处理器格式化结果
                self.test_results = self.data_processor.sort_data_by_nit(self.test_results)
            else:
                # 如果没有灰阶数据，使用亮度数据
                self.test_results = self.brightness_data
            
            if self.logger:
                self.logger.info("LTPS测试执行完成")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"LTPS测试执行失败: {e}")
            return False
    
    def save_results(self, additional_info: Optional[Dict] = None) -> str:
        """
        保存LTPS测试结果
        
        Args:
            additional_info: 额外信息
            
        Returns:
            保存的文件路径
        """
        try:
            # 添加LTPS特有的额外信息
            ltps_info = {
                'black_threshold_count': len(self.threshold_data_black),
                'white_threshold_count': len(self.threshold_data_white),
                'grey_data_count': len(self.grey_data)
            }
            
            if additional_info:
                ltps_info.update(additional_info)
            
            # 保存结果
            filepath = self.file_manager.save_test_results(
                test_data=self.test_results,
                test_type=self.get_test_type(),
                brightness_data=self.brightness_data,
                additional_info=ltps_info
            )
            
            return filepath
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"保存LTPS测试结果失败: {e}")
            return ""


def run_ltps_test(desktop_test: bool = False, enable_inspection: bool = True) -> bool:
    """
    运行LTPS测试的便捷函数
    
    Args:
        desktop_test: 是否为桌面测试
        enable_inspection: 是否启用灰阶校验
        
    Returns:
        测试是否成功
    """
    test = LTpsTest(desktop_test=desktop_test, enable_inspection=enable_inspection)
    return test.execute_test()
