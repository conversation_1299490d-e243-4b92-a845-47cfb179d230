# 灰阶自动化测试工具 v2.0

一个用于Android设备灰阶测试的自动化工具，支持LTPO和LTPS两种显示技术的测试。

## 版本信息

**当前版本**: v2.0.0
**发布日期**: 2025年8月6日

### v2.0.0 重大更新

🎉 **完全重构版本** - 全面提升代码质量和可维护性

#### 🏗️ 架构改进
- **模块化设计**: 按功能重新组织代码结构
- **配置驱动**: 支持外部配置文件管理
- **统一日志**: 集中的日志管理系统
- **错误处理**: 完善的异常处理机制

#### 📁 新的项目结构
```
newGreyScaleTest/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   ├── test_logic/        # 测试逻辑
│   ├── data/              # 数据处理
│   └── utils/             # 工具模块
├── config/                # 配置文件
├── assets/                # 资源文件
├── output/                # 输出目录
├── tests/                 # 测试代码
└── docs/                  # 文档
```

#### ✨ 新增功能
- **设备配置文件**: 支持不同设备的个性化配置
- **数据验证**: 完整的测试数据验证机制
- **结果格式化**: 多种格式的结果输出
- **模块化测试**: 独立的LTPO和LTPS测试模块

#### 🔧 技术改进
- **代码复用**: 消除重复代码，提高代码质量
- **类型安全**: 完善的类型注解
- **单元测试**: 支持单元测试框架
- **依赖管理**: 标准化的依赖管理

## 功能特性

### 🎯 测试类型
- **LTPO测试**: 支持可变刷新率显示技术测试
- **LTPS测试**: 支持传统LTPS显示技术测试
- **桌面测试**: 支持在系统桌面进行测试
- **灰阶校验**: 可选的灰阶策略验证

### 📱 设备支持
- 自动检测设备类型和配置
- 支持多种VIVO设备型号
- 自动获取root权限
- 智能设备状态管理

### 📊 数据收集
- 实时NIT值监控
- **优化的FPS数据采集**：多次采样机制，提高检测准确性
- 亮度梯度测试
- 灰阶门限检测
- **智能统计分析**：置信度评估和异常值检测

### 🌐 网络环境控制
- **自动网络隔离**：测试期间自动开启飞行模式
- **WiFi管理**：智能控制WiFi开关状态
- **环境恢复**：测试完成后自动恢复网络设置
- **稳定性保障**：避免网络干扰影响测试结果

## 安装和使用

### 环境要求
- Python 3.8+
- Android设备（已开启USB调试）
- ADB工具

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python src/main.py
```

### 打包程序
```bash
pyinstaller main.spec
```

## 配置说明

### 主配置文件 (config/default.json)
```json
{
    "test_timeout": 300,
    "retry_count": 3,
    "wait_time": 2,
    "brightness_range": {"min": 1, "max": 255},
    "fps_modes": {"smart": 1, "standard": 60, "high": 120}
}
```

### 设备配置文件 (config/device_profiles.json)
支持为不同设备型号配置特定参数。

## 使用指南

1. **连接设备**: 确保Android设备已连接并开启USB调试
2. **启动程序**: 运行主程序，程序会自动初始化设备
3. **选择测试**: 根据设备类型选择LTPO或LTPS测试
4. **查看结果**: 测试完成后在output/results目录查看结果

## 输出文件

- **测试结果**: `output/results/设备名_时间戳_out_result.txt`
- **日志文件**: `output/logs/app_时间戳.log`
- **最新日志**: `output/logs/latest.log`

## 开发指南

### 项目结构说明
- `src/core/`: 核心功能模块（ADB控制、设备管理、UI自动化）
- `src/test_logic/`: 测试逻辑模块（基础测试类、LTPO/LTPS测试）
- `src/data/`: 数据处理模块（数据收集、处理、文件管理）
- `src/utils/`: 工具模块（配置管理、日志、常量）

### 扩展开发
1. 继承`BaseTest`类实现新的测试类型
2. 在`config/device_profiles.json`中添加新设备配置
3. 使用`DataProcessor`类处理测试数据
4. 通过`FileManager`类保存结果

## 版本历史

### v1.1.2 (2025年5月19日)
1. 重构灰阶代码，删除冗余代码
2. 优化测试结果一致性问题
3. 新增桌面灰阶测试功能
4. 优化LTPO测试逻辑
5. 新增跳过灰阶校验功能
6. 新增自动关闭自动亮度选项
7. 优化结果文件输出格式

### v2.0.0 (2025年8月6日)
1. **完全重构**: 模块化架构设计
2. **配置驱动**: 外部配置文件支持
3. **代码质量**: 消除重复代码，提高可维护性
4. **错误处理**: 完善的异常处理机制
5. **日志系统**: 统一的日志管理
6. **设备管理**: 智能设备初始化和状态管理
7. **数据处理**: 完整的数据验证和处理流程
8. **文档完善**: 详细的使用和开发文档

## 技术支持

如有问题或建议，请查看日志文件或联系开发团队。