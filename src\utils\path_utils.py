"""
路径工具模块
提供跨平台的路径处理功能
"""

import os
import sys
from typing import Optional


class PathManager:
    """路径管理器"""
    
    def __init__(self):
        self._project_root = None
    
    @property
    def project_root(self) -> str:
        """获取项目根目录"""
        if self._project_root is None:
            # 从当前文件位置推断项目根目录
            current_file = os.path.abspath(__file__)
            # src/utils/path_utils.py -> 项目根目录
            self._project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        return self._project_root
    
    def get_assets_dir(self) -> str:
        """获取资源目录路径"""
        return os.path.join(self.project_root, "assets")
    
    def get_images_dir(self) -> str:
        """获取图片目录路径"""
        return os.path.join(self.project_root, "assets", "images")
    
    def get_apk_dir(self) -> str:
        """获取APK目录路径"""
        return os.path.join(self.project_root, "assets", "apk")
    
    def get_output_dir(self) -> str:
        """获取输出目录路径"""
        return os.path.join(self.project_root, "output")
    
    def get_results_dir(self) -> str:
        """获取结果目录路径"""
        return os.path.join(self.project_root, "output", "results")
    
    def get_logs_dir(self) -> str:
        """获取日志目录路径"""
        return os.path.join(self.project_root, "output", "logs")
    
    def get_config_dir(self) -> str:
        """获取配置目录路径"""
        return os.path.join(self.project_root, "config")
    
    def get_atx_apk_path(self) -> str:
        """获取ATX APK文件路径"""
        return os.path.join(self.get_apk_dir(), "ATX.apk")
    
    def ensure_dir_exists(self, dir_path: str) -> bool:
        """
        确保目录存在
        
        Args:
            dir_path: 目录路径
            
        Returns:
            目录是否存在或创建成功
        """
        try:
            os.makedirs(dir_path, exist_ok=True)
            return True
        except Exception:
            return False
    
    def ensure_output_dirs(self) -> bool:
        """
        确保所有输出目录存在
        
        Returns:
            是否成功创建所有目录
        """
        dirs_to_create = [
            self.get_output_dir(),
            self.get_results_dir(),
            self.get_logs_dir()
        ]
        
        success = True
        for dir_path in dirs_to_create:
            if not self.ensure_dir_exists(dir_path):
                success = False
        
        return success
    
    def get_device_image_path(self, filename: str) -> str:
        """
        获取设备上的图片路径（Android路径格式）
        
        Args:
            filename: 文件名
            
        Returns:
            设备上的文件路径
        """
        # Android设备路径始终使用正斜杠
        return f"/sdcard/static_grey/{filename}"
    
    def normalize_path(self, path: str) -> str:
        """
        标准化路径（处理路径分隔符）
        
        Args:
            path: 原始路径
            
        Returns:
            标准化后的路径
        """
        return os.path.normpath(path)
    
    def is_file_exists(self, filepath: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            filepath: 文件路径
            
        Returns:
            文件是否存在
        """
        return os.path.isfile(filepath)
    
    def is_dir_exists(self, dirpath: str) -> bool:
        """
        检查目录是否存在
        
        Args:
            dirpath: 目录路径
            
        Returns:
            目录是否存在
        """
        return os.path.isdir(dirpath)
    
    def get_file_size(self, filepath: str) -> Optional[int]:
        """
        获取文件大小
        
        Args:
            filepath: 文件路径
            
        Returns:
            文件大小（字节），如果文件不存在返回None
        """
        try:
            return os.path.getsize(filepath)
        except (OSError, FileNotFoundError):
            return None
    
    def list_files_in_dir(self, dirpath: str, extensions: Optional[list] = None) -> list:
        """
        列出目录中的文件
        
        Args:
            dirpath: 目录路径
            extensions: 文件扩展名列表，如 ['.jpg', '.png']
            
        Returns:
            文件列表
        """
        try:
            if not self.is_dir_exists(dirpath):
                return []
            
            files = []
            for filename in os.listdir(dirpath):
                filepath = os.path.join(dirpath, filename)
                if os.path.isfile(filepath):
                    if extensions is None:
                        files.append(filepath)
                    else:
                        file_ext = os.path.splitext(filename)[1].lower()
                        if file_ext in extensions:
                            files.append(filepath)
            
            return sorted(files)
            
        except Exception:
            return []
    
    def get_relative_path(self, filepath: str) -> str:
        """
        获取相对于项目根目录的相对路径
        
        Args:
            filepath: 绝对文件路径
            
        Returns:
            相对路径
        """
        try:
            return os.path.relpath(filepath, self.project_root)
        except ValueError:
            # 如果无法计算相对路径，返回原路径
            return filepath
    
    def join_path(self, *parts) -> str:
        """
        连接路径组件
        
        Args:
            *parts: 路径组件
            
        Returns:
            连接后的路径
        """
        return os.path.join(*parts)
    
    def get_filename_without_ext(self, filepath: str) -> str:
        """
        获取不带扩展名的文件名
        
        Args:
            filepath: 文件路径
            
        Returns:
            不带扩展名的文件名
        """
        return os.path.splitext(os.path.basename(filepath))[0]
    
    def get_file_extension(self, filepath: str) -> str:
        """
        获取文件扩展名
        
        Args:
            filepath: 文件路径
            
        Returns:
            文件扩展名（包含点号）
        """
        return os.path.splitext(filepath)[1]
    
    def create_timestamped_filename(self, base_name: str, extension: str) -> str:
        """
        创建带时间戳的文件名
        
        Args:
            base_name: 基础文件名
            extension: 文件扩展名
            
        Returns:
            带时间戳的文件名
        """
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        return f"{base_name}_{timestamp}.{extension.lstrip('.')}"


# 全局路径管理器实例
path_manager = PathManager()


def get_project_root() -> str:
    """获取项目根目录的便捷函数"""
    return path_manager.project_root


def get_assets_path(*parts) -> str:
    """获取资源文件路径的便捷函数"""
    return path_manager.join_path(path_manager.get_assets_dir(), *parts)


def get_output_path(*parts) -> str:
    """获取输出文件路径的便捷函数"""
    return path_manager.join_path(path_manager.get_output_dir(), *parts)


def ensure_output_dirs() -> bool:
    """确保输出目录存在的便捷函数"""
    return path_manager.ensure_output_dirs()
