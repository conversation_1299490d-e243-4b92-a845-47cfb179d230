"""
数据处理模块
负责测试数据的处理和分析
"""

from typing import List, Dict, Any, Optional, Tuple
from ..utils.logger import get_logger


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def merge_brightness_grey_data(self, brightness_data: List[Dict], grey_data: List[Dict]) -> List[Dict]:
        """
        合并亮度和灰阶数据
        
        Args:
            brightness_data: 亮度测试数据
            grey_data: 灰阶测试数据
            
        Returns:
            合并后的数据列表
        """
        merged_data = []
        
        try:
            for brightness_item in brightness_data:
                matching_grey_items = [
                    grey_item for grey_item in grey_data 
                    if grey_item.get('fps') == brightness_item.get('fps')
                ]
                
                for grey_item in matching_grey_items:
                    merged_item = {**brightness_item, **grey_item}
                    merged_data.append(merged_item)
            
            if self.logger:
                self.logger.info(f"合并数据完成，共 {len(merged_data)} 条记录")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"合并数据失败: {e}")
        
        return merged_data
    
    def merge_ltps_data(self, brightness_data: List[Dict], grey_data: List[Dict]) -> List[Dict]:
        """
        合并LTPS测试数据
        
        Args:
            brightness_data: 亮度测试数据
            grey_data: 灰阶测试数据
            
        Returns:
            合并后的数据列表
        """
        merged_data = []
        
        try:
            min_length = min(len(brightness_data), len(grey_data))
            
            for i in range(min_length):
                merged_item = {**grey_data[i], **brightness_data[i]}
                merged_data.append(merged_item)
            
            if self.logger:
                self.logger.info(f"LTPS数据合并完成，共 {len(merged_data)} 条记录")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"LTPS数据合并失败: {e}")
        
        return merged_data
    
    def sort_data_by_nit(self, data: List[Dict], reverse: bool = False) -> List[Dict]:
        """
        按NIT值排序数据
        
        Args:
            data: 数据列表
            reverse: 是否降序排列
            
        Returns:
            排序后的数据列表
        """
        try:
            sorted_data = sorted(
                data,
                key=lambda x: int(str(x.get('nit', 0))) if str(x.get('nit', 0)).isdigit() else 0,
                reverse=reverse
            )
            return sorted_data
        except Exception as e:
            if self.logger:
                self.logger.error(f"数据排序失败: {e}")
            return data
    
    def filter_data_by_fps(self, data: List[Dict], target_fps: int) -> List[Dict]:
        """
        按FPS值过滤数据
        
        Args:
            data: 数据列表
            target_fps: 目标FPS值
            
        Returns:
            过滤后的数据列表
        """
        try:
            filtered_data = [item for item in data if item.get('fps') == target_fps]
            return filtered_data
        except Exception as e:
            if self.logger:
                self.logger.error(f"数据过滤失败: {e}")
            return []
    
    def find_brightness_threshold(self, data: List[Dict]) -> Optional[Dict]:
        """
        查找亮度门限
        
        Args:
            data: 排序后的数据列表
            
        Returns:
            门限数据字典
        """
        try:
            if len(data) < 2:
                return None
            
            # 查找FPS变化的临界点
            for i in range(len(data) - 1):
                current_fps = data[i].get('fps', 0)
                next_fps = data[i + 1].get('fps', 0)
                
                if current_fps != next_fps:
                    return {
                        'threshold_before': data[i],
                        'threshold_after': data[i + 1],
                        'brightness_threshold': data[i].get('brightness', 0)
                    }
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"查找亮度门限失败: {e}")
            return None
    
    def calculate_statistics(self, data: List[Dict]) -> Dict[str, Any]:
        """
        计算数据统计信息
        
        Args:
            data: 数据列表
            
        Returns:
            统计信息字典
        """
        if not data:
            return {}
        
        try:
            nit_values = [item.get('nit', 0) for item in data if item.get('nit') is not None]
            fps_values = [item.get('fps', 0) for item in data if item.get('fps') is not None]
            brightness_values = [item.get('brightness', 0) for item in data if item.get('brightness') is not None]
            
            stats = {
                'total_records': len(data),
                'nit_range': {
                    'min': min(nit_values) if nit_values else 0,
                    'max': max(nit_values) if nit_values else 0,
                    'avg': sum(nit_values) / len(nit_values) if nit_values else 0
                },
                'fps_range': {
                    'min': min(fps_values) if fps_values else 0,
                    'max': max(fps_values) if fps_values else 0,
                    'unique_values': list(set(fps_values)) if fps_values else []
                },
                'brightness_range': {
                    'min': min(brightness_values) if brightness_values else 0,
                    'max': max(brightness_values) if brightness_values else 0
                }
            }
            
            return stats
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"计算统计信息失败: {e}")
            return {}
    
    def validate_data(self, data: List[Dict]) -> Tuple[bool, List[str]]:
        """
        验证数据完整性
        
        Args:
            data: 数据列表
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        if not data:
            errors.append("数据为空")
            return False, errors
        
        required_fields = ['nit', 'fps', 'brightness']
        
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                errors.append(f"记录 {i} 不是字典类型")
                continue
            
            for field in required_fields:
                if field not in item:
                    errors.append(f"记录 {i} 缺少字段: {field}")
                elif item[field] is None:
                    errors.append(f"记录 {i} 字段 {field} 为空")
                elif not isinstance(item[field], (int, float)):
                    try:
                        # 尝试转换为数字
                        float(item[field])
                    except (ValueError, TypeError):
                        errors.append(f"记录 {i} 字段 {field} 不是有效数字: {item[field]}")
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def remove_duplicates(self, data: List[Dict], key_fields: List[str] = None) -> List[Dict]:
        """
        移除重复数据
        
        Args:
            data: 数据列表
            key_fields: 用于判断重复的字段列表
            
        Returns:
            去重后的数据列表
        """
        if not data:
            return data
        
        if key_fields is None:
            key_fields = ['nit', 'fps', 'brightness']
        
        try:
            seen = set()
            unique_data = []
            
            for item in data:
                # 创建用于比较的键
                key = tuple(item.get(field, None) for field in key_fields)
                
                if key not in seen:
                    seen.add(key)
                    unique_data.append(item)
            
            if self.logger and len(unique_data) != len(data):
                self.logger.info(f"移除了 {len(data) - len(unique_data)} 条重复数据")
            
            return unique_data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"去重失败: {e}")
            return data
    
    def interpolate_missing_data(self, data: List[Dict], field: str) -> List[Dict]:
        """
        插值填补缺失数据
        
        Args:
            data: 数据列表
            field: 需要插值的字段
            
        Returns:
            插值后的数据列表
        """
        if not data:
            return data
        
        try:
            # 简单的线性插值实现
            for i, item in enumerate(data):
                if item.get(field) is None or item.get(field) == 0:
                    # 查找前后有效值
                    prev_value = None
                    next_value = None
                    
                    # 向前查找
                    for j in range(i - 1, -1, -1):
                        if data[j].get(field) is not None and data[j].get(field) != 0:
                            prev_value = data[j].get(field)
                            break
                    
                    # 向后查找
                    for j in range(i + 1, len(data)):
                        if data[j].get(field) is not None and data[j].get(field) != 0:
                            next_value = data[j].get(field)
                            break
                    
                    # 插值
                    if prev_value is not None and next_value is not None:
                        item[field] = (prev_value + next_value) / 2
                    elif prev_value is not None:
                        item[field] = prev_value
                    elif next_value is not None:
                        item[field] = next_value
            
            return data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"插值失败: {e}")
            return data
