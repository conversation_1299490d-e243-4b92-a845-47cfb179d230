"""
ADB控制器模块
提供Android设备的ADB操作功能
"""

import subprocess
import time
import os
from typing import Optional, List, Tuple
from src.utils.constants import ADB_COMMANDS, ERROR_MESSAGES, SUCCESS_MESSAGES
from src.utils.logger import get_logger


class ADBController:
    """ADB控制器类"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def execute_command(self, command: str, capture_output: bool = False) -> Optional[str]:
        """
        执行ADB命令

        Args:
            command: ADB命令
            capture_output: 是否捕获输出

        Returns:
            命令输出（如果capture_output为True）
        """
        try:
            if capture_output:
                # 在Windows系统上处理编码问题
                import platform
                if platform.system() == "Windows":
                    # Windows系统可能返回GBK编码
                    result = subprocess.run(command, shell=True, stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE, encoding="gbk", errors="ignore")
                    output = result.stdout.strip()
                    # 尝试转换为UTF-8
                    try:
                        output = output.encode('gbk').decode('utf-8', errors='ignore')
                    except:
                        pass
                    return output
                else:
                    result = subprocess.run(command, shell=True, stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE, encoding="utf-8", errors="ignore")
                    return result.stdout.strip()
            else:
                subprocess.run(command, shell=True)
                return None
        except Exception as e:
            if self.logger:
                self.logger.error(f"执行ADB命令失败: {command}, 错误: {e}")
            return None
    
    def check_device_connected(self) -> bool:
        """检查是否有设备连接"""
        try:
            result = self.execute_command(ADB_COMMANDS['devices'], capture_output=True)
            if not result:
                if self.logger:
                    self.logger.error("无法执行adb devices命令，请检查ADB是否正确安装")
                return False

            lines = result.splitlines()
            if self.logger:
                self.logger.debug(f"ADB devices输出: {result}")

            # 检查是否有设备连接
            device_count = 0
            for line in lines[1:]:  # 跳过标题行
                if line.strip() and '\tdevice' in line:
                    device_count += 1
                elif line.strip() and '\toffline' in line:
                    if self.logger:
                        self.logger.warning("检测到离线设备，请检查设备连接")
                elif line.strip() and '\tunauthorized' in line:
                    if self.logger:
                        self.logger.error("设备未授权，请在设备上允许USB调试")

            if device_count == 0:
                if self.logger:
                    self.logger.error("未检测到已连接的设备，请检查：")
                    self.logger.error("1. 设备是否通过USB连接到电脑")
                    self.logger.error("2. 设备是否开启了USB调试")
                    self.logger.error("3. 是否在设备上允许了此电脑的调试权限")
                return False

            if self.logger:
                self.logger.info(f"检测到 {device_count} 个已连接的设备")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"检查设备连接时发生错误: {e}")
            return False
    
    def get_root_permission(self) -> bool:
        """获取root权限"""
        if not self.check_device_connected():
            raise ValueError(ERROR_MESSAGES['NO_DEVICE'])
        
        root_type = self.check_root_type()
        if root_type == "root":
            return self._export_root()
        else:
            return self._vivo_root()
    
    def check_root_type(self) -> str:
        """检查root类型"""
        result = self.execute_command("adb shell whoami", capture_output=True)
        return result if result else "unknown"
    
    def _export_root(self) -> bool:
        """外销设备获取root权限"""
        try:
            result = self.execute_command(ADB_COMMANDS['root'], capture_output=True)
            if "adbd is already running as root" in result:
                return True
            
            # 等待设备连接
            subprocess.run(["adb", "wait-for-device"], check=True)
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"外销root权限获取失败: {e}")
            return False
    
    def _vivo_root(self) -> bool:
        """VIVO设备获取root权限"""
        try:
            self.execute_command(ADB_COMMANDS['vivo_root'])
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"VIVO root权限获取失败: {e}")
            return False
    
    def wake_up_screen(self) -> None:
        """唤醒屏幕"""
        self.execute_command(ADB_COMMANDS['wake_screen'])

    def unlock_screen(self) -> None:
        """解锁屏幕"""
        # 先按Home键，再按Menu键来解锁屏幕
        self.execute_command(ADB_COMMANDS['home_key'])
        time.sleep(0.5)
        self.execute_command(ADB_COMMANDS['unlock_screen'])
        time.sleep(0.5)
        self.execute_command(ADB_COMMANDS['unlock_screen'])

    def get_screen_lock_status(self) -> str:
        """
        获取屏幕锁定状态

        Returns:
            屏幕状态字符串
        """
        result = self.execute_command(ADB_COMMANDS['get_screen_lock_status'], capture_output=True)
        return result if result else "unknown"

    def set_stay_awake(self, enabled: bool) -> None:
        """
        设置充电时屏幕常亮模式

        Args:
            enabled: True为开启常亮，False为关闭常亮
        """
        command = ADB_COMMANDS['stay_awake_on'] if enabled else ADB_COMMANDS['stay_awake_off']
        self.execute_command(command)

    def get_stay_awake_status(self) -> str:
        """
        获取充电时屏幕常亮状态

        Returns:
            常亮状态值（7为开启，0为关闭）
        """
        result = self.execute_command(ADB_COMMANDS['get_stay_awake'], capture_output=True)
        return result if result else "0"
    
    def set_brightness(self, brightness: int) -> None:
        """
        设置屏幕亮度
        
        Args:
            brightness: 亮度值 (1-255)
        """
        if not 1 <= brightness <= 255:
            raise ValueError("亮度值必须在1-255之间")
        
        command = f"adb shell settings put system screen_brightness {brightness}"
        self.execute_command(command)
    
    def get_auto_brightness_status(self) -> bool:
        """获取自动亮度状态"""
        result = self.execute_command(ADB_COMMANDS['get_auto_brightness'], capture_output=True)
        return result == '1' if result else False
    
    def enable_auto_brightness(self) -> None:
        """开启自动亮度"""
        self.execute_command(ADB_COMMANDS['auto_brightness_on'])
    
    def disable_auto_brightness(self) -> None:
        """关闭自动亮度"""
        self.execute_command(ADB_COMMANDS['auto_brightness_off'])
    
    def set_dark_mode(self, enabled: bool) -> None:
        """设置深色模式"""
        command = ADB_COMMANDS['dark_mode_on'] if enabled else ADB_COMMANDS['dark_mode_off']
        self.execute_command(command)
    
    def get_dark_mode_status(self) -> str:
        """获取深色模式状态"""
        result = self.execute_command(ADB_COMMANDS['get_dark_mode'], capture_output=True)
        return result if result else "unknown"
    
    def set_fps_mode(self, fps_mode: int) -> None:
        """
        设置帧率模式
        
        Args:
            fps_mode: 帧率模式 (1=智能, 60=标准, 120=高帧率)
        """
        if fps_mode == 1:
            command = ADB_COMMANDS['set_fps_smart']
        elif fps_mode == 60:
            command = ADB_COMMANDS['set_fps_60']
        elif fps_mode == 120:
            command = ADB_COMMANDS['set_fps_120']
        else:
            raise ValueError("不支持的帧率模式")
        
        self.execute_command(command)
    
    def upload_file(self, local_path: str, remote_path: str) -> bool:
        """
        上传文件到设备
        
        Args:
            local_path: 本地文件路径
            remote_path: 设备文件路径
            
        Returns:
            上传是否成功
        """
        if not os.path.exists(local_path):
            if self.logger:
                self.logger.error(f"本地文件不存在: {local_path}")
            return False
        
        command = f"adb push {local_path} {remote_path}"
        self.execute_command(command)
        return True
    
    def install_apk(self, apk_path: str) -> bool:
        """
        安装APK文件
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            安装是否成功
        """
        if not os.path.exists(apk_path):
            if self.logger:
                self.logger.error(f"APK文件不存在: {apk_path}")
            return False
        
        command = f"adb install -r {apk_path}"
        result = self.execute_command(command, capture_output=True)
        return "Success" in result if result else False
    
    def is_app_installed(self, package_name: str) -> bool:
        """
        检查应用是否已安装
        
        Args:
            package_name: 应用包名
            
        Returns:
            应用是否已安装
        """
        command = f"adb shell pm list packages | grep {package_name}"
        result = self.execute_command(command, capture_output=True)
        return package_name in result if result else False
    
    def force_stop_app(self, package_name: str) -> None:
        """
        强制停止应用
        
        Args:
            package_name: 应用包名
        """
        command = f"adb shell am force-stop {package_name}"
        self.execute_command(command)
    
    def get_current_activity(self) -> Optional[str]:
        """获取当前活动的应用包名"""
        command = 'adb shell "dumpsys window | grep mCurrentFocus"'
        result = self.execute_command(command, capture_output=True)
        if result and "/" in result:
            try:
                package_name = result.split('{')[1].split('/')[0].split(' ')[-1].strip()
                return package_name
            except (IndexError, AttributeError):
                pass
        return None

    def stop_current_app(self):
        """ 关闭当前APP """
        current_app_name = self.get_current_activity()
        if current_app_name:
            self.force_stop_app(current_app_name)
            if self.logger:
                self.logger.info(f"Stopping app {current_app_name}...")
        else:
            if self.logger:
                self.logger.warning("No active application found.")
    
    def get_device_info(self) -> dict:
        """获取设备信息"""
        info = {}
        
        # 获取设备名称
        result = self.execute_command("adb shell getprop ro.product.model", capture_output=True)
        info['device_name'] = result if result else "Unknown"
        
        # 获取软件版本
        result = self.execute_command("adb shell getprop ro.vivo.product.version", capture_output=True)
        info['software_version'] = result if result else "Unknown"
        
        # 获取Android版本
        result = self.execute_command("adb shell getprop ro.build.version.release", capture_output=True)
        info['android_version'] = result if result else "Unknown"
        
        return info
    
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 500) -> None:
        """
        滑动操作
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 滑动持续时间（毫秒）
        """
        command = f"adb shell input swipe {start_x} {start_y} {end_x} {end_y} {duration}"
        self.execute_command(command)
