"""
日志工具模块
提供统一的日志记录功能
"""

import logging
import os
import sys
import builtins
from datetime import datetime
from typing import Optional
from .constants import LOGS_DIR, LOG_FORMAT, LOG_LEVEL


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self.logger = None
        self.log_file = None
        self.original_print = builtins.print
        self.log_print = None
    
    def setup_logging(self, log_level: str = LOG_LEVEL) -> str:
        """
        设置日志记录

        Args:
            log_level: 日志级别

        Returns:
            日志文件路径
        """
        # 如果已经设置过日志，直接返回
        if self.logger and self.log_file:
            return self.log_file

        # 使用绝对路径确保跨平台兼容性
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        logs_dir = os.path.join(project_root, LOGS_DIR)

        # 创建日志目录
        os.makedirs(logs_dir, exist_ok=True)

        # 固定日志文件名（按需求）
        self.log_file = os.path.join(logs_dir, "app.log")

        # 获取或创建日志记录器
        logger_name = 'grey_scale_test'
        self.logger = logging.getLogger(logger_name)

        # 防止重复设置：如果已有处理器，先清除
        if self.logger.handlers:
            for handler in self.logger.handlers[:]:
                self.logger.removeHandler(handler)

        # 设置记录器级别为 DEBUG，便于文件记录所有级别
        self.logger.setLevel(logging.DEBUG)

        # 防止日志传播到根记录器（避免重复输出）
        self.logger.propagate = False

        # 创建文件处理器（DEBUG级别，详细格式）
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            fmt='%(asctime)s %(levelname)s %(name)s %(threadName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)

        # 创建控制台处理器（默认 INFO，简洁格式）。支持 APP_VERBOSE=1/true 提升为 DEBUG。
        console_handler = logging.StreamHandler(stream=sys.__stdout__)
        console_level = logging.INFO
        verbose_env = os.getenv('APP_VERBOSE', '').strip().lower()
        if verbose_env in ('1', 'true', 'yes', 'on'):
            console_level = logging.DEBUG
        console_handler.setLevel(console_level)
        console_formatter = logging.Formatter('[%(levelname)s] %(message)s')
        console_handler.setFormatter(console_formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # 创建最新日志链接（固定文件名下可忽略，但保持兼容）
        self._create_latest_log_link()

        # 记录启动信息（写文件为 DEBUG 全量，控制台仅 WARNING 以上）
        self.logger.debug(f"程序启动 - 日志文件: {self.log_file}")
        self.logger.debug(f"系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.debug("=" * 50)

        # 设置全局print重定向
        self._setup_print_override()

        return self.log_file
    
    def _create_latest_log_link(self) -> None:
        """创建最新日志链接"""
        try:
            # 使用绝对路径确保跨平台兼容性
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            logs_dir = os.path.join(project_root, LOGS_DIR)
            latest_log = os.path.join(logs_dir, "latest.log")

            if os.path.exists(latest_log):
                os.remove(latest_log)

            # 在Windows上创建副本，在Unix/Linux上创建软链接
            if os.name == 'nt':  # Windows
                import shutil
                shutil.copy2(self.log_file, latest_log)
            else:  # Unix/Linux
                # 切换到日志目录创建相对路径的软链接
                old_cwd = os.getcwd()
                try:
                    os.chdir(logs_dir)
                    os.symlink(os.path.basename(self.log_file), "latest.log")
                finally:
                    os.chdir(old_cwd)
        except Exception as e:
            if self.logger:
                self.logger.warning(f"创建最新日志链接失败: {e}")
    
    def _setup_print_override(self) -> None:
        """设置print函数重定向"""
        # 检查是否已经重定向过
        if hasattr(builtins.print, '_is_redirected'):
            return

        def global_print_override(*args, sep=' ', end='\n', file=None, flush=False):
            message = sep.join(map(str, args))

            # 只有当没有指定文件或指定为stdout时才记录到日志
            if file is None or file is sys.stdout:
                if self.logger:
                    # 将 print 统一作为 INFO 记录（仅写入文件；控制台按 WARNING 默认不显示）
                    self.logger.info(message)
            else:
                # 如果指定了其他文件，直接写入
                self.original_print(*args, sep=sep, end=end, file=file, flush=flush)

        # 标记已重定向
        global_print_override._is_redirected = True
        self.log_print = global_print_override
        builtins.print = global_print_override
    
    def restore_print(self) -> None:
        """恢复原始print函数"""
        builtins.print = self.original_print
    
    def get_logger(self) -> Optional[logging.Logger]:
        """获取日志记录器"""
        return self.logger
    
    def get_log_file(self) -> Optional[str]:
        """获取日志文件路径"""
        return self.log_file
    
    def info(self, message: str) -> None:
        """记录信息日志"""
        if self.logger:
            self.logger.info(message)
    
    def warning(self, message: str) -> None:
        """记录警告日志"""
        if self.logger:
            self.logger.warning(message)
    
    def error(self, message: str, exc_info: bool = False) -> None:
        """记录错误日志"""
        if self.logger:
            self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str) -> None:
        """记录调试日志"""
        if self.logger:
            self.logger.debug(message)


# 全局日志管理器实例
logger_manager = LoggerManager()


def get_logger() -> Optional[logging.Logger]:
    """获取全局日志记录器"""
    return logger_manager.get_logger()


def setup_logging(log_level: str = LOG_LEVEL) -> str:
    """设置全局日志记录"""
    return logger_manager.setup_logging(log_level)


def restore_print() -> None:
    """恢复原始print函数"""
    logger_manager.restore_print()
