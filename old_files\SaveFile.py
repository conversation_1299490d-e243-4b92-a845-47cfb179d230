import json
from datetime import datetime
from ADB import ADB

class SaveFile:
    def __init__(self, brightness_data, grey_scale, brightnessThreshold=None):
        self.brightness_data = brightness_data
        self.grey_scale = grey_scale
        self.brightnessThreshold = brightnessThreshold
        self.adb = ADB()

    def merge_data(self):
        merged_data = []
        for brightness_item in self.brightness_data:
            matching_grey_items = [
                grey_item for grey_item in self.grey_scale if grey_item['fps'] == brightness_item['fps']]
            for grey_item in matching_grey_items:
                merged_item = {**brightness_item, **grey_item}
                merged_data.append(merged_item)
        return merged_data

    def merge_data_ltps(self):
        merged_data = []
        for i in range(len(self.brightness_data)):
            # 合并两个字典，如果键值相同，以，后一个字典为准
            merged_data.append(
                {**self.grey_scale[i], **self.brightness_data[i]}
            )
        return merged_data

    def format_data_ltps(self, merged_data):
        """
        格式化LTPS灰阶测试数据 - 优化版 (始终输出原始数据，格式类似亮度门限输出)

        根据亮度门限测试的关键点数据，生成用户友好的亮度范围、
        对应灰阶值和刷新率的描述。处理连续相同NIT值的情况，
        避免生成无效范围条目。此版本始终将原始输入数据作为字符串列表
        添加到结果数据中，格式类似亮度门限输出，并包含头部尾部标记，便于调试。
        """
        # 不需要导入 json 模块，因为不进行 JSON 格式化
        result_data = []
        current_range_start_nit = "0"

        # --- 尝试执行主要的格式化逻辑 ---
        # 使用 try...except 块来捕获在主要格式化过程中发生的任何未预期错误
        try:
            # 检查输入数据是否为空 - 如果为空，只在结果中添加一个信息条目，但不执行主要格式化循环
            if not merged_data:
                result_data.append({"_Info_": "输入的合并数据显示为空，无法格式化主要结果。"})
            else:
                # 如果输入数据不为空，执行主要的格式化循环
                for i, item in enumerate(merged_data):
                    # 获取当前数据点的NIT值（转换为字符串以便比较）
                    item_nit_str = str(item.get("nit"))

                    # 只有当当前数据点的NIT与当前范围的起始NIT不同时，才生成一个新的范围条目。
                    if item_nit_str != str(current_range_start_nit):

                        item_grey_value = item.get("grey_value")
                        grey_value_str = ""
                        # --- 针对灰阶值转换的 try 块 ---
                        try:
                            if item_grey_value is not None:
                                # 尝试将灰阶值转换为整数进行准确比较
                                grey_int = int(item_grey_value)
                                if grey_int == 0:
                                    grey_value_str = "全灰阶"
                                else:
                                    grey_value_str = str(item_grey_value) + "Grey"
                            else:
                                 # 如果 grey_value 为 None，使用一个占位符表示
                                 grey_value_str = "N/A Grey"
                                 # 注意：这里的错误不会单独收集到列表中，只会影响当前条目格式

                        except (ValueError, TypeError) as e: # 捕获特定的转换错误
                             # 如果转换失败，使用原始值并加上 "Grey" 作为回退
                             # 注意：这里的错误不会单独收集到列表中
                             print(f"警告: 格式化时，灰阶值 '{item_grey_value}' 格式异常，无法转换为整数，位于索引 {i}。错误信息: {e}") # 仍然打印警告到控制台，便于即时发现问题
                             grey_value_str = str(item_grey_value) + "Grey" # 格式化失败时的回退处理


                        # 确定亮度范围字符串：从当前范围的起始NIT到当前数据点的NIT
                        nit_range_str = f"{current_range_start_nit}-{item_nit_str}nit"

                        # 获取当前范围的刷新率（使用当前数据点的FPS）
                        item_fps_str = str(item.get("fps"))

                        # 将格式化后的条目（字典）添加到结果列表中
                        result_data.append({
                            "设置亮度/nit": nit_range_str,
                            "灰阶值/Grey": grey_value_str,
                            "刷新率/fps": item_fps_str
                        })

                        # 更新下一个范围的起始NIT为当前数据点的NIT
                        current_range_start_nit = item_nit_str


                # --- 处理最后一个亮度范围：大于最后一个记录点的NIT ---
                # 只有当原始输入数据不为空时，才执行此处理
                if merged_data: # redundant check, but safe
                    last_item_in_merged_data = merged_data[-1]
                    last_item_fps = str(last_item_in_merged_data.get("fps"))
                    last_item_nit = str(last_item_in_merged_data.get("nit"))

                    # 只有当 merged_data 中的最后一项的FPS不是60时，才添加 ">last_nit" 范围到 60fps 的条目
                    if last_item_fps != "60":
                         result_data.append({
                            "设置亮度/nit": ">" + last_item_nit + "nit",
                            "灰阶值/Grey": "全灰阶", # 通常高亮度范围是全灰阶
                            "刷新率/fps": "60"
                         })
                    # 保持与原始逻辑一致，如果最后一项FPS已经是60，则不额外添加 >last_nit 条目。


        except Exception as e: # 捕获在主要格式化逻辑（for 循环和后续处理）中发生的任何未预期错误
            # 添加一个错误信息字符串条目到结果数据中
            result_data.append(f"--- 格式化过程中发生未预期的错误: {e} ---")
            # 打印一个警告到控制台 (可选)
            print(f"\n警告: 在格式化 LTPS 测试结果时发生未预期的错误，错误信息已添加到结果数据中。错误信息: {e}")


        # --- 始终添加原始输入数据到结果数据列表的末尾 ---
        # 添加头部字符串标记
        result_data.append("--- 原始输入数据 (merged_data) ---")
        # 遍历原始数据列表，将每个字典转换为字符串并添加到结果中
        if merged_data: # 只有当原始输入数据不为空时才遍历
            for item in merged_data:
                # 将字典直接转换为字符串表示形式
                # 格式类似 {"key": value, ...}
                result_data.append(str(item))
        else:
             # 如果原始数据为空，添加一个表示空列表的字符串
             result_data.append("[]")
        # 添加尾部字符串标记
        result_data.append("--- 原始输入数据结束 ---")


        return result_data # 返回包含格式化结果、可能错误信息和原始数据字符串的混合列表

    def format_data(self, merged_data):
        result_data = []
        current_nit = "0"
        merged_data_len = len(merged_data)
        """
        0-10nit		10fps	0-10nit 	≤28 Grey 锁10fps
                                        >28-44 Grey 锁5fps
                                        >44 Grey	锁1fps
        
        10-204nit	5fps 	10-204nit 	≤28 Grey 锁10fps
                                        >28-44 Grey 锁5fps
                                        >44 Grey	锁1fps
        
        >204nit 	1fps 	>204nit 	≤28 Grey 锁5fps
                                        >28 Grey 锁1fps 
        """
        """
        result_data.append({
                        "设置亮度/nit": ">" + item.get("nit") + "nit",
                        "灰阶值/Grey": "全灰阶",
                        "刷新率/fps": str(item.get("fps"))
                    })
        """
        grey_value_s = "0"
        nit_s = "0"
        for i, item in enumerate(merged_data):
            brightness = item.get("brightness")
            fps = item.get("fps")
            grey_value_arr = item.get("grey_value_arr")
            grey_value_arr_len = len(grey_value_arr)

            if i == merged_data_len - 1:
                nit = item.get("nit")
            else:
                nit = merged_data[i + 1].get("nit")

            """ 处理灰阶结果 Start """
            grey_str = "\n"
            for j, grey_item in enumerate(grey_value_arr):
                if j == grey_value_arr_len - 1:
                    grey_fps = grey_item.get("fps")
                    index = grey_item.get("index")
                else:
                    grey_fps = grey_value_arr[j].get("fps")
                    index = grey_value_arr[j + 1].get("index")

                if not (index == "0" or index == 0):
                    index = index - 1
                grey_value = int(index) * 4
                if grey_value == 0:
                    grey_value = "全灰阶"
                if j == 0:
                    # 处理第一个数据
                    grey_str += "≤" + str(grey_value) + " Grey 锁" + str(grey_fps) + "fps\n"
                elif j == grey_value_arr_len - 1:
                    # 处理最后一个数据
                    grey_str += ">" + str(grey_value) + " Grey 锁" + str(grey_fps) + "fps\n"
                else:
                    # 处理中间数据
                    grey_str += ">" + str(grey_value_s) + "-" + str(grey_value) + " Grey 锁" + str(grey_fps) + "fps\n"
                grey_value_s = grey_value
            """ 处理灰阶结果 End """
            if i == 0:
                # 处理第一个数据
                result_data.append({
                    "设置亮度/nit": "0-" + str(nit) + "nit",
                    "刷新率/fps": str(fps) + "fps",
                    "灰阶值/Grey": grey_str,
                })
            elif i == merged_data_len - 1:
                # 处理最后一个数据
                result_data.append({
                    "设置亮度/nit": ">" + str(nit) + "nit",
                    "刷新率/fps": str(fps) + "fps",
                    "灰阶值/Grey": grey_str,
                })
            else:
                # 处理中间数据
                result_data.append({
                    "设置亮度/nit": str(nit_s) + "-" + str(nit) + "nit",
                    "刷新率/fps": str(fps) + "fps",
                    "灰阶值/Grey": grey_str,
                })
            nit_s = nit
        root_str = "\n"
        raw_data_description = "参数说明:\n" + \
            "- nit: 亮度值\n" + \
            "- fps: 帧率\n" + \
            "- brightness: 背光值\n" + \
            "- range: 测试时取的背光值 (用于灰阶门限测试的亮度设定)\n" + \
            "- grey_value: 灰阶值 (在对应亮度下找到的灰阶门限值)\n" + \
            "- index: 灰阶图片索引 (通常与 grey_value 相关，表示系统图片编号)\n"
        root_str += raw_data_description
        root_str += "灰阶门限\n"
        root_str += self.format_data_for_txt(merged_data)
        result_data.append(root_str)

        return result_data

    def format_data_for_txt(self, merged_data):
        """ 辅助方法 """
        # 开始构建输出字符串
        output = ""

        # 处理每个主元素
        for i, item in enumerate(merged_data):
            # 添加每个主元素的开头
            output += "{'nit': '" + str(item['nit']) + "', 'fps': '" + str(item['fps']) + "', 'brightness': " + str(
                item['brightness']) + ", 'grey_value_arr': \n\t\t[\n"

            # 处理grey_value_arr中的每个元素
            for j, grey_item in enumerate(item['grey_value_arr']):
                output += "\t\t\t{'index': " + str(grey_item['index']) + ", 'fps': '" + str(grey_item['fps']) + "'}"

                # 如果不是最后一个元素，添加逗号
                if j < len(item['grey_value_arr']) - 1:
                    output += ", \n"
                else:
                    output += "\n"

            # 添加grey_value_arr的结束括号
            output += "\t\t]\n\t}"

            # 如果不是最后一个主元素，添加逗号和换行
            if i < len(merged_data) - 1:
                output += ", \n"

        return output

    # 使用示例
    # with open('output.txt', 'w') as f:
    #     f.write(format_data_for_txt(merged_data))

    def save_to_file(self, result_data, filename='out_result.txt', title=None):
        """
        将测试结果保存到文件。处理包含字典和字符串的混合列表。
        包含原始数据及其参数说明。

        Args:
            result_data (list): 包含格式化结果(字典)和调试信息/原始数据(字符串)的列表。
            filename (str): 输出文件名。
            title (str, optional): 文件的主要标题。
        """
        # 获取当前时间并格式化
        current_time = datetime.now().strftime('%Y-%m-%d %H-%M-%S')

        # 尝试获取设备名称，如果失败则使用默认值
        device_name = "UnknownDevice"
        if hasattr(self, 'adb') and hasattr(self.adb, 'get_device_name'):
            try:
                name = self.adb.get_device_name()
                if name:
                    device_name = str(name).replace("\n", "").strip()
            except Exception as e:
                print(f"警告: 保存文件时无法获取设备名称: {e}")
        else:
             print("警告: SaveFile 实例没有可用的 adb 属性或 get_device_name 方法。")


        # 构建完整的文件名
        filename = f"{device_name}_{current_time}_{filename}"

        # 尝试获取软件版本，如果失败则使用默认值
        version = "UnknownVersion"
        if hasattr(self, 'adb') and hasattr(self.adb, 'get_software_version'):
            try:
                 v = self.adb.get_software_version()
                 if v:
                    version = str(v).replace("\n", "").strip()
            except Exception as e:
                print(f"警告: 保存文件时无法获取软件版本: {e}")


        # 打开文件进行写入
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                # 写入标题和设备信息
                if title:
                    file.write(f"设备名称:{device_name}\n")
                    file.write(f"软件版本:{version}\n")

                    # 尝试获取深色模式状态
                    dark_mode_status = "状态未知"
                    if hasattr(self.adb, 'is_dark_mode'):
                        try:
                            flag_dark = self.adb.is_dark_mode()
                            if flag_dark and "yes" in str(flag_dark).lower():
                                dark_mode_status = "开启"
                            else:
                                dark_mode_status = "关闭" # Assuming anything other than "yes" means off
                        except Exception as e:
                             print(f"警告: 保存文件时无法获取深色模式状态: {e}")
                             dark_mode_status = f"获取失败({e})"

                    file.write(f"深色模式：{dark_mode_status}\n")
                    file.write(f"{title}\n") # 写入用户提供的主标题


                # 写入亮度门限值 (假设 self.brightnessThreshold 是一个包含字典的列表)
                # 增加健壮性检查，确保 self.brightnessThreshold 存在且是列表
                if hasattr(self, 'brightnessThreshold') and isinstance(self.brightnessThreshold, list):
                    file.write("\n亮度门限:\n") # 在此添加一个空行和头部，与测试结果区分
                    try:
                        # 排序 assumes each item is a dict with 'nit' that can be int-casted
                        # 使用 get('nit', '0') 并转换为 int，处理可能缺失 'nit' 或格式错误的情况
                        sorted_threshold = sorted(self.brightnessThreshold, key=lambda x: int(str(x.get('nit', 0))), reverse=False)
                        for item in sorted_threshold:
                             # 确保 item 是一个字典 before attempting to print as dict-like string
                             if isinstance(item, dict):
                                 # 将字典转换为字符串表示形式，类似 {"key": value, ...}
                                 # 使用 str() 来获得这种格式，与原始数据项格式一致
                                 file.write(str(item) + '\n')
                             else:
                                 # 如果亮度门限数据中混入了非字典类型，打印警告
                                 file.write(f"--- 警告: 亮度门限数据中存在非字典条目类型: {type(item)} - {item} ---\n")

                    except Exception as e:
                        print(f"警告: 写入亮度门限数据时发生错误: {e}")
                        file.write(f"--- 写入亮度门限数据时发生错误: {e} ---\n")

                # LTPO亮度门限
                # 增加健壮性检查，确保 self.brightness_data 存在且是列表
                if hasattr(self, 'brightness_data') and isinstance(self.brightness_data, list):
                    file.write("\n亮度门限:\n")  # 添加空行和标题，与其他部分区分
                    try:
                        # 排序亮度数据，按 'nit' 值从小到大排序
                        # 使用安全的方式获取 'nit' 值并转换为整数
                        sorted_brightness = sorted(self.brightness_data,
                                                   key=lambda x: int(str(x.get('nit', 0))) if str(
                                                       x.get('nit', 0)).isdigit() else 0,
                                                   reverse=False)

                        for item in sorted_brightness:
                            # 确保 item 是字典类型
                            if isinstance(item, dict):
                                # 将字典转换为字符串表示形式
                                file.write(str(item) + '\n')
                            else:
                                # 如果数据中混入了非字典类型，打印警告
                                file.write(f"--- 警告: 亮度数据中存在非字典条目类型: {type(item)} - {item} ---\n")

                    except Exception as e:
                        print(f"警告: 写入亮度数据时发生错误: {e}")
                        file.write(f"--- 写入亮度数据时发生错误: {e} ---\n")

                # 写入主要的测试结果和原始数据
                file.write("\n测试结果:\n\n") # 在此添加头部，与亮度门限区分

                # 遍历由 format_data_ltps 返回的混合列表
                for item in result_data:
                    if isinstance(item, dict):
                        # 如果条目是字典（正常格式化结果条目），按原方式处理
                        line_items = []
                        try:
                            # 格式化字典的键值对
                            line_items = [f'{key}: {value}' for key, value in item.items()]
                        except Exception as e:
                            # 如果格式化字典条目时发生错误，写入错误信息
                            file.write(f"--- 格式化结果字典条目时发生错误: {e} - {item} ---\n")
                            continue # 跳过当前有问题的字典条目，继续处理下一个

                        # 将格式化后的键值对用换行符连接，并在块后添加两个换行符
                        line = '\n'.join(line_items) + '\n\n'
                        file.write(line)

                    elif isinstance(item, str):
                        # 如果条目是字符串（头部、尾部、错误信息、原始数据项等），直接写入一行
                        file.write(item + '\n') # 在字符串后添加一个换行符

                        # --- 检查是否是原始数据头部，如果是，立即写入参数说明 ---
                        if item == "--- 原始输入数据 (merged_data) ---":
                            raw_data_description = (
                                "参数说明:\n"
                                "- nit: 亮度值\n"
                                "- fps: 帧率\n"
                                "- brightness: 背光值\n"
                                "- range: 测试时取的背光值 (用于灰阶门限测试的亮度设定)\n"
                                "- grey_value: 灰阶值 (在对应亮度下找到的灰阶门限值)\n"
                                "- index: 灰阶图片索引 (通常与 grey_value 相关，表示系统图片编号)\n"
                            )
                            file.write(raw_data_description + '\n') # 写入说明文字，并在末尾加换行
                        # --- 检查结束 ---


                    # else: 可以选择在这里处理其他未预期的条目类型，或者忽略
                    # else:
                    #     file.write(f"--- 无法识别的结果条目类型: {type(item)} - {item} ---\n")


            # 如果文件成功打开和写入，打印成功信息
            print(f'Result saved to {filename}')

        except Exception as file_error:
             # 捕获文件操作本身的错误（如权限问题）
             print(f"严重错误: 保存结果文件 '{filename}' 时发生错误: {file_error}")


# if __name__ == '__main__':
#     data_merger = SaveFile()
#     merged_data = data_merger.merge_data()
#     formatted_data = data_merger.format_data(merged_data)
#     data_merger.save_to_file(formatted_data)
#     print(formatted_data)