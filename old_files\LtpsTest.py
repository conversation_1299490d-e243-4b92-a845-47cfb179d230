import sys

from GetLogData import GetLogData
from ADB import ADB
from LtpsTestLogic import LtpsTestLogic
from SaveFile import SaveFile


def main(desktop_test=False, isInspected=True):
    get_log_data = GetLogData()
    get_log_data.start_threads()
    adb = ADB()
    logic = LtpsTestLogic(get_log_data=get_log_data, desktop_test=desktop_test)
    print("检查是否安装ATX...")
    is_install = adb.is_app_installed("uiautomator")
    if not is_install:
        # 安装ATX
        print("未安装ATX\n正在安装ATX...")
        adb.install_atx("ATX.apk")

    try:
        print("获取root权限...")
        adb.get_root()
        print("唤醒屏幕...")
        adb.wake_up_screen()
        print("上传测试文件...")
        adb.upload_file()
        # 判断是否开启灰阶校验
        if isInspected:
            # 1.判断是否存在灰阶
            if not logic.check_grey_scale_strategy():
                sys.exit("该设备不存在灰阶策略")
            print("该设备存在灰阶策略，开始进行灰阶亮度门限测试...")

        # 2.灰阶亮度门限测试
        threshold_data_black = logic.brightness_threshold_test(image="BLACK")
        # 2453
        # threshold_data_black = [{'nit': '123', 'fps': 90, 'brightness': 88}, {'nit': '123', 'fps': 60, 'brightness': 89}]
        # 2415
        # threshold_data_black = [{'nit': '118', 'fps': 90, 'brightness': 87}, {'nit': '122', 'fps': 60, 'brightness': 88}]
        print("黑色图片边界值打印")
        print(threshold_data_black)

        threshold_data_white = logic.brightness_threshold_test(image="WHITE")
        # 2453
        # threshold_data_white = [{'nit': '123', 'fps': 90, 'brightness': 18}, {'nit': '123', 'fps': 60, 'brightness': 19}]
        # 2415
        # threshold_data_white = [{'nit': '24', 'fps': 90, 'brightness': 18}, {'nit': '25', 'fps': 60, 'brightness': 19}]
        print("白色图片边界值打印")
        print(threshold_data_white)

        threshold_data = [threshold_data_black[0], threshold_data_white[0]]
        threshold_data.sort(key=lambda x: int(x['nit']), reverse=False)
        # 2453
        # threshold_data = [{'nit': '123', 'fps': 90, 'brightness': 88}, {'nit': '123', 'fps': 90, 'brightness': 18}]
        # 2415
        # threshold_data = [{'nit': '24', 'fps': 90, 'brightness': 18}, {'nit': '118', 'fps': 90, 'brightness': 87}]
        print("排序后")
        print(threshold_data)

        # 3.灰阶门限测试
        gray_data = logic.gray_scale_threshold_test(threshold_data)
        # 2453
        # gray_data = [{'nit': '123', 'fps': 90, 'brightness': 88, 'range': 44, 'grey_value': 68, 'index': 17.0}, {'nit': '123', 'fps': 90, 'brightness': 18, 'range': 53, 'grey_value': 68, 'index': 17.0}]
        # 2415
        # gray_data = [{'nit': '24', 'fps': 90, 'brightness': 18, 'range': 9, 'grey_value': 0, 'index': 0.0}, {'nit': '118', 'fps': 90, 'brightness': 87, 'range': 53, 'grey_value': 64, 'index': 16.0}]
        print("灰阶门限")

        print(gray_data)
        """ 输出结果文件 """
        save_file = SaveFile(brightness_data=threshold_data, grey_scale=gray_data, brightnessThreshold=threshold_data_black + threshold_data_white)
        # 灰阶策略有所调整，该逻辑弃用，重新调整合并逻辑
        # merged_data = save_file.merge_data_ltps()
        formatted_data = save_file.format_data_ltps(gray_data)
        save_file.save_to_file(result_data=formatted_data, title="LTPS灰阶测试结果")

    except Exception as e:
        print(e)
        input("按回车键退出程序...")
        sys.exit()


if __name__ == '__main__':
    main(desktop_test=False, isInspected=True)
