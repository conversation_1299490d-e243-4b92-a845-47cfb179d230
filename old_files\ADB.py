""" ADB.py """
import random
import re
import subprocess
import time


class ADB:
    def __init__(self):
        pass

    def enable_auto_brightness(self):
        """开启自动亮度调节功能"""
        self.user_run_adb("adb shell settings put system screen_brightness_mode 1")

    def disable_auto_brightness(self):
        """关闭自动亮度调节功能"""
        self.user_run_adb("adb shell settings put system screen_brightness_mode 0")

    def get_auto_brightness_status(self):
        """获取自动亮度调节状态

        返回:
            bool: True 表示自动亮度已开启，False 表示自动亮度已关闭
        """
        str = self.user_run_adb_stdout("adb shell settings get system screen_brightness_mode").strip()
        if self.user_run_adb_stdout("adb shell settings get system screen_brightness_mode").strip() == '1':
            return True
        return False

    def page_left_to(self):
        """向左滑动"""
        self.user_run_adb("adb shell input swipe 800 500 200 500")

    def page_right_to(self):
        """向右滑动"""
        self.user_run_adb("adb shell input swipe 200 500 800 500")

    def open_app(self):
        """启动app"""
        self.user_run_adb("adb shell am start -n com.vivo.greyscaletest/com.vivo.greyscaletest.SecondActivity")

    def stop_app(self):
        """关闭app"""
        self.user_run_adb("adb shell am force-stop com.vivo.greyscaletest")

    def get_activity_info(self):
        """ 获取当前屏幕中的应用信息 """
        value = self.user_run_adb_stdout('adb shell "dumpsys window | grep mCurrentFocus"')
        match = re.search(r'mCurrentFocus=Window{\w+ u\d+ (\S+)/', value)
        if match:
            package_name = match.group(1)
            return package_name
        else:
            return None

    def stop_current_app(self):
        """ 关闭当前app """
        # 获取当前屏幕信息
        package_name = self.get_activity_info()
        if package_name:
            subprocess.run("adb shell am force-stop " + package_name, shell=True)

    def start_dark_mode(self):
        """开启深色模式"""
        self.user_run_adb_stdout("adb shell cmd uimode night yes")

    def stop_dark_mode(self):
        """关闭深色模式"""
        self.user_run_adb_stdout("adb shell cmd uimode night no")

    def is_dark_mode(self):
        """ 判断是否开启深色模式 """
        return self.user_run_adb_stdout("adb shell cmd uimode night")

    def start_lcm_fps_1(self):
        """ 开启智能帧率 """
        self.user_run_adb_stdout("adb shell settings put global vivo_screen_refresh_rate_mode 1")

    def start_lcm_fps_60(self):
        """ 开启标准帧率 """
        self.user_run_adb_stdout("adb shell settings put global vivo_screen_refresh_rate_mode 60")

    def start_lcm_fps_120(self):
        """ 开启高帧率 """
        self.user_run_adb_stdout("adb shell settings put global vivo_screen_refresh_rate_mode 120")

    def upload_file(self, location="static_grey", remote="/sdcard/"):
        """ 上传文件到设备存储中 """
        self.user_run_adb("adb push " + location + " " + remote)  # 延时5秒，等待文件上传完毕

    def open_system_image(self, file_name, file_suffix="jpg"):
        """ 使用系统相册打开指定图片 """
        self.user_run_adb(
            'adb shell am start -a android.intent.action.VIEW -n com.vivo.gallery/com.android.gallery3d.app.Gallery -d "file:///sdcard/static_grey/' + file_name + '.' + file_suffix + '" -t "image/*"')

    def close_system_image(self, package_name="com.vivo.gallery"):
        """ 关闭系统相册 """
        self.user_run_adb("adb shell am force-stop " + package_name)

    def click_double(self, x=600, y=600, delay=0.01):
        """
        使用ADB命令模拟屏幕上的双击操作。

        参数:
        x, y - 要双击的屏幕坐标
        delay - 两次点击之间的延迟时间，单位为秒
        """
        # 构建单次点击的ADB命令
        tap_cmd = "adb shell input tap {x} {y}".format(x=x, y=y)

        # 执行第一次点击
        subprocess.run(tap_cmd, shell=True)
        # 等待指定的延迟时间
        time.sleep(delay)
        # 执行第二次点击
        subprocess.run(tap_cmd, shell=True)

    def click_one(self, x=600, y=600, delay=0.01):
        """
        使用ADB命令模拟屏幕上的双击操作。

        参数:
        x, y - 要双击的屏幕坐标
        delay - 两次点击之间的延迟时间，单位为秒
        """
        # 构建单次点击的ADB命令
        tap_cmd = "adb shell input tap {x} {y}".format(x=x, y=y)

        # 执行第一次点击
        subprocess.run(tap_cmd, shell=True)

    def on_home(self):
        """返回主界面"""
        self.user_run_adb("adb shell input keyevent KEYCODE_HOME")

    def on_menu(self):
        """返回主菜单"""
        self.user_run_adb("adb shell input keyevent KEYCODE_MENU")

    def get_root(self):
        """获取root权限"""
        # 检查是否存在设备
        if not self.check_device_connected():
            raise ValueError("无法连接设备，请确认设备状态...")

        if self.check_root_command() == "root":
            # 外销获取root权限
            self.export_root()
            pass
        else:
            subprocess.run("adb vivoroot", shell=True)

    def check_device_connected(self):
        """检查是否有设备连接"""
        result = subprocess.run("adb devices", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                encoding="utf-8")
        lines = result.stdout.splitlines()
        # 如果输出的行数大于1，表示有设备连接
        if len(lines) > 2:
            return True
        else:
            return False

    def get_software_version(self):
        """获取软件版本"""
        return self.user_run_adb_stdout("adb shell getprop ro.vivo.product.version")

    def check_root_command(self):
        # 判断内外销，手机，使用不同的命令
        result = self.user_run_adb_stdout("adb shell getprop ro.vivo.product.version")
        version = result
        # 正则表达式，匹配 "W" 后面的数字
        pattern = r"W(\d+)"

        # 执行匹配操作
        matches = re.search(pattern, version)

        # 检查是否有匹配，如果有，打印匹配出的数字
        if matches:
            number_after_w = matches.group(1)
        else:
            print("内外销判断失败")
            return "vivoroot"
        if int(number_after_w) > 10:
            return "root"
        else:
            return "vivoroot"

    def export_root(self):
        """ 外销获取root权限 """
        res = self.user_run_adb_stdout("adb root")
        if "adbd is already running as root" in res:
            return
        # 等待ADB设备连接
        subprocess.run(["adb", "wait-for-device"], check=True)

        # 获取并打印软件版本
        sw_version_result = subprocess.run(["adb", "shell", "getprop", "ro.vivo.product.version"],
                                           stdout=subprocess.PIPE,
                                           text=True, check=True)
        sw_version = sw_version_result.stdout.strip()

        # 获取vusbd_disabled属性值
        vusbd_disabled_result = subprocess.run(["adb", "shell", "getprop", "ro.vivo.disable.vusbd"],
                                               stdout=subprocess.PIPE,
                                               text=True, check=True)
        vusbd_disabled = int(vusbd_disabled_result.stdout.strip())

        # 根据vusbd_disabled的值决定如何Root设备
        if vusbd_disabled == 1:
            subprocess.run(["fastboot", "--version"], check=True)
            subprocess.run(["adb", "reboot", "bootloader"], check=True)
            subprocess.run(["fastboot", "vivoroot"], check=True)
            subprocess.run(["ping", "****", "-n", "2"], stdout=subprocess.DEVNULL)
            subprocess.run(["fastboot", "reboot"], check=True)
            subprocess.run(["adb", "wait-for-device"], check=True)
            subprocess.run(["adb", "root"], check=True)
            subprocess.run(["ping", "****", "-n", "2"], stdout=subprocess.DEVNULL)
        else:
            subprocess.run(["adb", "wait-for-device"], check=True)
            subprocess.run(["adb", "vivoroot"], check=True)
            subprocess.run(["ping", "****", "-n", "2"], stdout=subprocess.DEVNULL)
        while True:
            if "1" in self.is_system_status():
                time.sleep(10)
                return
            time.sleep(1)

    def check_adb_software_version(self):
        """检查adb软件版本_windows环境"""
        self.user_run_adb('adb shell getprop | findstr "ro.*.driver"')

    def get_device_name(self):
        """ 获取设备名称 """
        return self.user_run_adb_stdout('adb shell getprop ro.product.model')

    def get_fps(self, flag=False):
        """在windows环境下获取fps帧率"""
        # flag为True时，返回输出内容
        if flag:
            result = self.user_run_adb_stdout('adb shell dumpsys SurfaceFlinger | findstr "refresh-rate"')
            pattern = r"\d+"
            fps_arr = re.findall(pattern, result)
            fps = int(fps_arr[0])
            return fps
        else:
            self.user_run_adb_stdout('adb shell dumpsys SurfaceFlinger | findstr "refresh-rate"')

    def switch_brightness_255(self, brightness):
        """切换亮度_1-255"""
        self.user_run_adb('adb shell "settings put system screen_brightness ' + str(brightness) + '"')

    def reset_screen_policy(self):
        """重置屏幕策略"""
        subprocess.run('adb shell input keyevent KEYCODE_POWER', shell=True)
        time.sleep(1)
        self.on_menu()
        time.sleep(1)
        self.on_menu()

    # 唤醒屏幕
    def wake_up_screen(self):
        # subprocess.run('adb shell input keyevent KEYCODE_POWER', shell=True)
        # time.sleep(1)
        self.on_menu()
        time.sleep(1)
        self.on_menu()

    def is_system_status(self):
        """判断系统状态"""
        return self.user_run_adb_stdout("adb shell getprop sys.boot_completed")

    def get_ltpo_fps(self):
        """获取ltpo fps"""
        return self.user_run_adb_stdout("adb shell cat /sys/lcm/refresh_monitor")

    def get_lcm_resolution(self):
        """
        获取lcm分辨率
        :return: 分辨率 array [0] 宽 [1] 高
        """
        value = self.user_run_adb_stdout("adb shell wm size")
        split_value = value.split(":")[1].strip().split("x")
        return split_value

    def generate_swipe_events(self, screen_width, screen_height, distance, speed, direction='up',
                              output_file='swipe_events_vts_report_point.txt'):
        """
        生成模拟滑动事件数据并写入文本文件。

        :param screen_width: 屏幕宽度
        :param screen_height: 屏幕高度
        :param distance: 滑动总距离
        :param speed: 滑动速度（单位时间内的Y坐标变化量）
        :param direction: 滑动方向 ('up' 或 'down')
        :param output_file: 输出文件名
        :return: string 文件名称
        """
        # 计算起始坐标为屏幕中心
        start_x = (int(screen_width) // 2) * 10
        start_y = (int(screen_height) // 2) * 10

        # 根据方向调整Y坐标变化
        if direction == 'up':
            y_increment = -speed
        elif direction == 'down':
            y_increment = speed
        else:
            raise ValueError("Direction must be 'up' or 'down'.")

        finger_id = 0
        finger_state = 1  # 手指按下状态
        width = 4
        height = 4
        time_increment = 0.001  # 时间增量

        events = []
        current_y = start_y
        current_time = 1000.0

        # 生成滑动事件
        target_y = start_y + (distance if direction == 'down' else -distance)
        while (direction == 'up' and current_y > target_y) or (direction == 'down' and current_y < target_y):
            event = f"[{current_time:.5f}][{finger_id} {start_x} {current_y}][{finger_state}][1][{width}][{height}]"
            events.append(event)
            current_y += y_increment
            current_time += time_increment

        # 添加手指抬起事件
        finger_state = 0
        event = f"[{current_time:.5f}][{finger_id} {start_x} {current_y}][{finger_state}][0][{width}][{height}]"
        events.append(event)

        # 将事件写入文件
        with open(output_file, 'w') as file:
            for event in events:
                file.write(event + '\n')

        return output_file

    def install_atx(self, apk_path):
        """ 安装ATX """
        self.user_run_adb("adb install -r -g " + apk_path)

    def is_app_installed(self, package_name):
        """检查应用是否安装"""
        result = self.user_run_adb_stdout('adb shell "pm list packages | grep ' + package_name + '"')
        return package_name in result

    def user_run_adb_stdout(self, str):
        """运行命令，并捕获输出"""
        # 运行命令并捕获输出
        completed_process = subprocess.run(str, stdout=subprocess.PIPE, text=True, shell=True)
        # 返回输出内容
        return completed_process.stdout

    def user_run_adb(self, str):
        """用户自定义adb命令"""
        subprocess.run(str, shell=True)
