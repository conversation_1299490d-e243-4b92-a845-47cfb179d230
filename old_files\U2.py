import time

import uiautomator2 as u2
from ADB import ADB


class U2:
    def __init__(self):
        self.device = None
        self.watcher = None
        self.adb = ADB()
        self.connect()

    def connect(self):
        """ 连接设备 """
        self.device = u2.connect()
        self.watcher_when_system_init()

    def get_device_info(self):
        """ 获取设备信息 """
        return self.device.info()

    def get_device_dump(self):
        """ 获取设备界面信息 """
        return self.device.dump_hierarchy()

    def text_click(self, text):
        """ 点击文本 """
        element = self.device(text=text)
        if element.exists:
            element.click()

    def desc_click(self, desc):
        """ 点击描述 """
        element = self.device(description=desc)
        if element.exists:
            element.click()

    def text_or_desc_click(self, text):
        """ 点击符合text的文本，从text或description中匹配 """
        element = self.device(text=text)
        if element.exists:
            element.click()
            return
        element = self.device(description=text)
        if element.exists:
            element.click()
            return

    def is_element_text_exist(self, text):
        """ 判断文本是否存在 """
        return self.device(text=text).exists

    def is_element_desc_exist(self, desc):
        """ 判断文本是否存在 """
        return self.device(description=desc).exists

    def is_element_xpath_exist(self, xpath):
        """ 判断文本是否存在 """
        return self.device.xpath(xpath).exists

    def is_text_await_loading(self, text):
        """ 等待加载 """
        i = 0
        while True:
            if not self.is_element_text_exist(text):
                return True
            time.sleep(1)
            i += 1
            if i > 30:
                return False

    def is_xpath_await_loading(self, xpath):
        """ 等待加载 """
        i = 0
        while True:
            if not self.is_element_xpath_exist(xpath):
                return True
            time.sleep(1)
            i += 1
            if i > 30:
                return False
    def get_screen_center(self):
        """ 计算并返回屏幕中心位置 """
        screen_info = self.device.window_size()
        width = screen_info[0]
        height = screen_info[1]
        center_x = width // 2
        center_y = height // 2
        return center_x, center_y

    def custom_kinetic_scroll_x(self, start_x, end_x, y_position=None, speed=0.5, times=1):
        """ 根据指定的起始和结束位置进行水平滑动 """
        width, height = self.device.window_size()

        # 如果没有指定y_position，使用屏幕的垂直中心
        if y_position is None:
            y_position = height // 2

        # 确保x和y坐标在屏幕范围内
        if not (0 <= start_x <= width and 0 <= end_x <= width):
            raise ValueError("start_x and end_x must be within the screen width.")
        if not (0 <= y_position <= height):
            raise ValueError("y_position must be within the screen height.")

        for _ in range(times):
            self.device.swipe(start_x, y_position, end_x, y_position, duration=speed)

    def kinetic_scroll_x(self, direction="left", speed=0.5, times=1):
        """ 横向滑动 """
        width, height = self.device.window_size()
        center_y = height // 2

        if direction == "left":
            start_x = int(width * 0.8)
            end_x = int(width * 0.2)
        elif direction == "right":
            start_x = int(width * 0.2)
            end_x = int(width * 0.8)
        else:
            raise ValueError("Invalid direction for horizontal scroll. Use 'left' or 'right'.")

        for _ in range(times):
            self.device.swipe(start_x, center_y, end_x, center_y, duration=speed)

    def kinetic_scroll_y(self, direction="up", speed=0.5, times=1, centre_y: float=0.8):
        """
        纵向滑动
        :param direction: 滑动方向，'up' 或 'down'
        :param speed: 滑动速度，单位为秒
        :param times: 滑动次数
        :param centre_y: 屏幕中心y坐标的百分比，默认为屏幕的80%
        """
        width, height = self.device.window_size()
        center_x = width // 2

        if direction == "up":
            start_y = int(height * centre_y)
            end_y = int(height * 0.2)
        elif direction == "down":
            start_y = int(height * 0.2)
            end_y = int(height * centre_y)
        else:
            raise ValueError("Invalid direction for vertical scroll. Use 'up' or 'down'.")

        for _ in range(times):
            self.device.swipe(center_x, start_y, center_x, end_y, duration=speed)

    def drive_input_touch(self, direction='up', speed=10):
        """ 驱动模拟触摸事件 """
        lcm_value = self.adb.get_lcm_resolution()
        screen_width = lcm_value[0]
        screen_height = lcm_value[1]
        distance = 5000
        file_name = self.adb.generate_swipe_events(screen_width=screen_width, screen_height=screen_height, distance=distance, speed=speed, direction=direction)
        time.sleep(1)
        self.adb.user_run_adb('adb push ' + file_name + ' vendor/firmware')
        time.sleep(1)
        self.adb.user_run_adb('adb shell "echo ' + file_name + ' > /sys/class/vts/vivo_ts/points_inject"')
        time.sleep(5)

    def xpath_click(self, xpath):
        """ 使用XPath定位并点击 """
        element = self.device.xpath(xpath)
        if element.exists:
            element.click()

    def xpath_input_text(self, xpath, text):
        """ 使用XPath定位并输入文本 """
        element = self.device.xpath(xpath)
        if element.exists:
            element.set_text(text)

    def get_element_position(self, xpath=None, text=None):
        """ 根据XPath获取元素在页面上的x, y坐标  self.device(text=text)"""
        if xpath:
            element = self.device.xpath(xpath)
            if element.exists:
                bounds = element.info['bounds']
                # 计算元素的中心点坐标
                x = (bounds['left'] + bounds['right']) // 2
                y = (bounds['top'] + bounds['bottom']) // 2
                return x, y
            else:
                raise ValueError(f"Element with XPath '{xpath}' not found.")
        if text:
            element = self.device(text=text)
            if element.exists:
                bounds = element.info['bounds']
                # 计算元素的中心点坐标
                x = (bounds['left'] + bounds['right']) // 2
                y = (bounds['top'] + bounds['bottom']) // 2
                return x, y
            else:
                raise ValueError(f"Element with text '{text}' not found.")


    def watcher_when_system_init(self):
        """ 监听系统级控件初始化 """
        self.device.watcher.when("允许").click()
        self.device.watcher.when("同意").click()
        self.device.watcher.when("跳过").click()
        self.device.watcher.when("跳过广告").click()
        self.device.watcher.when("稍后再说").click()
        self.device.watcher.when("使用应用时允许").click()

    def watcher_when_add(self, text):
        """ 监听添加 """
        self.device.watcher.when(text).click()

    def watcher_when_xpath_add(self, xpath):
        """ 监听XPath添加 """
        self.device.watcher.when(xpath=xpath).click()

    def watcher_when_start(self):
        """ 启动监听 """
        self.device.watcher.start()

    def watcher_when_stop(self):
        """ 停止监听 """
        self.device.watcher.stop()

    def watcher_when_remove(self):
        """ 移除所有监听 """
        self.device.watcher.remove()

    def run(self):
        """ 运行操作"""
        # print(self.get_device_dump())
        # element = self.device.xpath('//*[@resource-id="com.ss.android.auto:id/fpu"]/android.widget.LinearLayout[1]/android.widget.TextView')
        # self.xpath_click('//*[@resource-id="com.ss.android.auto:id/fpu"]/android.widget.LinearLayout[1]/android.widget.TextView')

        pass
