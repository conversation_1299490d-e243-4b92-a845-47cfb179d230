"""
设备管理模块
负责设备的初始化、状态检查和基本操作
"""

import os
from typing import Optional, Dict, Any
from .adb_controller import ADBController
from ..utils.constants import (
    ATX_APK, APK_DIR, IMAGES_DIR, UIAUTOMATOR_PACKAGE,
    ERROR_MESSAGES, SUCCESS_MESSAGES
)
from ..utils.config import device_profile_manager
from ..utils.logger import get_logger


class DeviceManager:
    """设备管理器"""
    
    def __init__(self):
        self.adb = ADBController()
        self.logger = get_logger()
        self.device_info = {}
        self.device_profile = {}
    
    def initialize_device(self) -> bool:
        """
        初始化设备

        Returns:
            初始化是否成功
        """
        try:
            if self.logger:
                self.logger.info("开始设备初始化...")

            # 检查设备连接
            if not self.adb.check_device_connected():
                raise ValueError(ERROR_MESSAGES['NO_DEVICE'])

            # 获取设备信息
            self.device_info = self.adb.get_device_info()
            if self.logger:
                self.logger.info(f"设备信息: {self.device_info}")

            # 加载设备配置
            self._load_device_profile()

            # 获取root权限（可选，某些功能需要）
            try:
                if self.adb.get_root_permission():
                    if self.logger:
                        self.logger.info(SUCCESS_MESSAGES['ROOT_OBTAINED'])
                else:
                    if self.logger:
                        self.logger.warning("未能获取root权限，某些功能可能受限")
            except Exception as root_error:
                if self.logger:
                    self.logger.warning(f"Root权限获取失败: {root_error}，将以普通权限继续")

            # 唤醒屏幕
            try:
                self.adb.wake_up_screen()
                if self.logger:
                    self.logger.info("屏幕已唤醒")
            except Exception as wake_error:
                if self.logger:
                    self.logger.warning(f"唤醒屏幕失败: {wake_error}")

            # 检查并安装ATX
            try:
                if not self._ensure_atx_installed():
                    if self.logger:
                        self.logger.warning("ATX安装失败，UI自动化功能可能受限")
            except Exception as atx_error:
                if self.logger:
                    self.logger.warning(f"ATX检查失败: {atx_error}")

            # 上传测试文件
            try:
                if not self._upload_test_files():
                    if self.logger:
                        self.logger.warning("测试文件上传失败，请手动确保测试图片存在于设备中")
            except Exception as upload_error:
                if self.logger:
                    self.logger.warning(f"文件上传失败: {upload_error}")

            # 检查并关闭自动亮度
            try:
                if self.adb.get_auto_brightness_status():
                    if self.logger:
                        self.logger.info("检测到自动亮度开启，正在关闭...")
                    self.adb.disable_auto_brightness()
                    if self.logger:
                        self.logger.info("自动亮度已关闭")
            except Exception as brightness_error:
                if self.logger:
                    self.logger.warning(f"自动亮度设置失败: {brightness_error}")

            if self.logger:
                self.logger.info("设备初始化完成")

            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设备初始化失败: {e}")
            return False
    
    def _load_device_profile(self) -> None:
        """加载设备配置文件"""
        device_name = self.device_info.get('device_name', 'default')
        self.device_profile = device_profile_manager.get_profile(device_name)
        
        if not self.device_profile:
            # 如果没有找到设备配置，使用默认配置
            self.device_profile = device_profile_manager.get_profile('default')
            if self.logger:
                self.logger.warning(f"未找到设备 {device_name} 的配置，使用默认配置")
    
    def _ensure_atx_installed(self) -> bool:
        """确保ATX已安装"""
        if self.adb.is_app_installed(UIAUTOMATOR_PACKAGE):
            if self.logger:
                self.logger.info("ATX已安装")
            return True

        # 安装ATX
        if self.logger:
            self.logger.info("未安装ATX，正在安装...")

        # 使用绝对路径确保跨平台兼容性
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        atx_path = os.path.join(project_root, APK_DIR, ATX_APK)

        if self.logger:
            self.logger.debug(f"ATX APK路径: {atx_path}")

        if not os.path.exists(atx_path):
            if self.logger:
                self.logger.error(f"APK文件不存在: {atx_path}")
                self.logger.error("请确保ATX.apk文件位于assets/apk/目录中")
            return False

        if self.adb.install_apk(atx_path):
            if self.logger:
                self.logger.info(SUCCESS_MESSAGES['ATX_INSTALLED'])
            return True
        else:
            if self.logger:
                self.logger.error(ERROR_MESSAGES['ATX_INSTALL_FAILED'])
            return False
    
    def _upload_test_files(self) -> bool:
        """上传测试文件到设备"""
        try:
            remote_path = self.device_profile.get('remote_path', '/sdcard/')

            # 使用绝对路径确保跨平台兼容性
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            images_dir = os.path.join(project_root, IMAGES_DIR)

            if self.logger:
                self.logger.debug(f"测试图片目录: {images_dir}")
                self.logger.debug(f"设备目标路径: {remote_path}")

            # 上传灰阶图片
            if os.path.exists(images_dir):
                # 检查目录中是否有图片文件
                image_files = [f for f in os.listdir(images_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
                if not image_files:
                    if self.logger:
                        self.logger.warning(f"测试图片目录为空: {images_dir}")
                    return False

                if self.logger:
                    self.logger.info(f"找到 {len(image_files)} 个测试图片文件")

                success = self.adb.upload_file(images_dir, remote_path)
                if success and self.logger:
                    self.logger.info("测试文件上传成功")
                return success
            else:
                if self.logger:
                    self.logger.warning(f"测试文件目录不存在: {images_dir}")
                    self.logger.warning("请确保灰阶测试图片位于assets/images/目录中")
                return False

        except Exception as e:
            if self.logger:
                self.logger.error(f"上传测试文件失败: {e}")
            return False
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        return self.device_info.copy()
    
    def get_device_profile(self) -> Dict[str, Any]:
        """获取设备配置"""
        return self.device_profile.copy()
    
    def set_brightness(self, brightness: int) -> None:
        """设置屏幕亮度"""
        self.adb.set_brightness(brightness)
    
    def set_dark_mode(self, enabled: bool) -> None:
        """设置深色模式"""
        self.adb.set_dark_mode(enabled)
        if self.logger:
            status = "开启" if enabled else "关闭"
            self.logger.info(f"深色模式已{status}")
    
    def set_fps_mode(self, fps_mode: int) -> None:
        """设置帧率模式"""
        self.adb.set_fps_mode(fps_mode)
        if self.logger:
            mode_names = {1: "智能", 60: "标准", 120: "高"}
            mode_name = mode_names.get(fps_mode, "未知")
            self.logger.info(f"已切换到{mode_name}帧率模式")
    
    def get_dark_mode_status(self) -> str:
        """获取深色模式状态"""
        return self.adb.get_dark_mode_status()
    
    def force_stop_current_app(self) -> None:
        """强制停止当前应用"""
        current_app = self.adb.get_current_activity()
        if current_app:
            self.adb.force_stop_app(current_app)
            if self.logger:
                self.logger.info(f"已停止应用: {current_app}")
    
    def reset_screen_policy(self) -> None:
        """重置屏幕策略"""
        # 这里可以添加重置屏幕策略的具体实现
        # 目前保持与原代码兼容
        pass

    def ensure_screen_unlocked(self) -> bool:
        """
        确保屏幕处于解锁状态

        Returns:
            操作是否成功
        """
        try:
            # 先唤醒屏幕
            self.adb.wake_up_screen()
            if self.logger:
                self.logger.info("屏幕已唤醒")

            # 等待一下让屏幕完全唤醒
            import time
            time.sleep(1)

            # 解锁屏幕
            self.adb.unlock_screen()
            if self.logger:
                self.logger.info("屏幕解锁操作已执行")

            # 再等待一下确保解锁完成
            time.sleep(1)

            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"屏幕解锁失败: {e}")
            return False

    def set_screen_stay_awake(self, enabled: bool) -> bool:
        """
        设置屏幕常亮模式

        Args:
            enabled: True为开启常亮，False为关闭常亮

        Returns:
            操作是否成功
        """
        try:
            self.adb.set_stay_awake(enabled)
            status = "开启" if enabled else "关闭"
            if self.logger:
                self.logger.info(f"屏幕常亮模式已{status}")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设置屏幕常亮模式失败: {e}")
            return False

    def set_airplane_mode(self, enabled: bool) -> bool:
        """
        设置飞行模式

        Args:
            enabled: True为开启飞行模式，False为关闭飞行模式

        Returns:
            操作是否成功
        """
        try:
            from ..utils.constants import ADB_COMMANDS

            if enabled:
                result = self.adb.execute_command(ADB_COMMANDS['airplane_mode_on'])
            else:
                result = self.adb.execute_command(ADB_COMMANDS['airplane_mode_off'])

            status = "开启" if enabled else "关闭"
            if self.logger:
                self.logger.info(f"飞行模式已{status}")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设置飞行模式失败: {e}")
            return False

    def set_wifi(self, enabled: bool) -> bool:
        """
        设置WiFi开关

        Args:
            enabled: True为开启WiFi，False为关闭WiFi

        Returns:
            操作是否成功
        """
        try:
            from ..utils.constants import ADB_COMMANDS

            if enabled:
                result = self.adb.execute_command(ADB_COMMANDS['wifi_enable'])
            else:
                result = self.adb.execute_command(ADB_COMMANDS['wifi_disable'])

            status = "开启" if enabled else "关闭"
            if self.logger:
                self.logger.info(f"WiFi已{status}")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设置WiFi失败: {e}")
            return False

    def get_airplane_mode_status(self) -> bool:
        """
        获取飞行模式状态

        Returns:
            True表示飞行模式开启，False表示关闭
        """
        try:
            from ..utils.constants import ADB_COMMANDS

            result = self.adb.execute_command(ADB_COMMANDS['get_airplane_mode'], capture_output=True)
            if result:
                return result.strip() == '1'
            return False

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取飞行模式状态失败: {e}")
            return False

    def setup_test_network_environment(self) -> bool:
        """
        设置测试网络环境（开启飞行模式，关闭WiFi）

        Returns:
            操作是否成功
        """
        try:
            if self.logger:
                self.logger.info("正在设置测试网络环境...")

            # 开启飞行模式
            if not self.set_airplane_mode(True):
                if self.logger:
                    self.logger.warning("开启飞行模式失败")
                return False

            # 等待飞行模式生效
            import time
            time.sleep(2)

            # 关闭WiFi（确保网络完全断开）
            if not self.set_wifi(False):
                if self.logger:
                    self.logger.warning("关闭WiFi失败")
                # 不返回False，因为飞行模式已经开启

            if self.logger:
                self.logger.info("测试网络环境设置完成")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设置测试网络环境失败: {e}")
            return False

    def restore_network_environment(self) -> bool:
        """
        恢复网络环境（开启WiFi，关闭飞行模式）

        Returns:
            操作是否成功
        """
        try:
            if self.logger:
                self.logger.info("正在恢复网络环境...")

            # 开启WiFi
            if not self.set_wifi(True):
                if self.logger:
                    self.logger.warning("开启WiFi失败")

            # 等待WiFi启动
            import time
            time.sleep(2)

            # 关闭飞行模式
            if not self.set_airplane_mode(False):
                if self.logger:
                    self.logger.warning("关闭飞行模式失败")
                return False

            if self.logger:
                self.logger.info("网络环境恢复完成")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"恢复网络环境失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 恢复网络环境
            try:
                self.restore_network_environment()
                if self.logger:
                    self.logger.info("已恢复网络环境")
            except Exception as network_error:
                if self.logger:
                    self.logger.warning(f"恢复网络环境失败: {network_error}")

            # 恢复屏幕常亮设置
            try:
                self.set_screen_stay_awake(False)
                if self.logger:
                    self.logger.info("已恢复屏幕常亮设置")
            except Exception as stay_awake_error:
                if self.logger:
                    self.logger.warning(f"恢复屏幕常亮设置失败: {stay_awake_error}")

            # 停止当前应用
            self.force_stop_current_app()

            if self.logger:
                self.logger.info("设备清理完成")

        except Exception as e:
            if self.logger:
                self.logger.error(f"设备清理失败: {e}")


class DeviceFactory:
    """设备工厂类"""
    
    @staticmethod
    def create_device() -> DeviceManager:
        """
        创建设备管理器实例
        
        Returns:
            设备管理器实例
        """
        return DeviceManager()
