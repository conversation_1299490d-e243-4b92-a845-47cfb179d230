# 故障排除指南

## 常见问题及解决方案

### 1. 日志重复输出问题

**问题描述**: 每条日志信息都被输出了两次

**原因**: 日志处理器重复添加或print函数重定向冲突

**解决方案**:
- ✅ 已修复：在logger.py中添加了重复检查机制
- ✅ 已修复：设置了logger.propagate = False防止传播到根记录器
- ✅ 已修复：在print重定向中添加了重复检测

### 2. 设备连接失败

**问题描述**: 程序无法连接到Android设备

**可能原因**:
- ADB工具未安装或未添加到PATH
- 设备未开启USB调试
- 设备未授权此电脑进行调试
- USB连接问题

**解决步骤**:

#### 步骤1: 检查ADB工具
```bash
# 运行诊断脚本
python run_diagnostic.py

# 或手动检查
adb version
```

如果提示"adb不是内部或外部命令"：
1. 下载Android SDK Platform Tools
2. 解压到任意目录（如C:\adb）
3. 将该目录添加到系统PATH环境变量

#### 步骤2: 检查设备设置
1. 在Android设备上开启"开发者选项"：
   - 设置 → 关于手机 → 连续点击"版本号"7次
2. 开启"USB调试"：
   - 设置 → 开发者选项 → USB调试
3. 连接USB线后，在设备上允许调试权限

#### 步骤3: 验证连接
```bash
adb devices
```
应该显示类似：
```
List of devices attached
ABC123456789    device
```

### 3. UTF-8编码错误

**问题描述**: 在数据收集线程中出现编码错误

**原因**: Windows系统ADB输出使用GBK编码，但代码强制使用UTF-8解码

**解决方案**:
- ✅ 已修复：在ADB命令执行中添加了平台检测
- ✅ 已修复：Windows系统使用GBK编码，其他系统使用UTF-8
- ✅ 已修复：添加了errors='ignore'参数处理无法解码的字符

### 4. Windows路径问题

**问题描述**: 在Windows系统中出现路径相关错误，如：
```
APK文件不存在: assets/apk\ATX.apk
```

**原因**: 路径分隔符不兼容，Windows使用`\`而代码中使用了`/`

**解决方案**:
- ✅ 已修复：使用`os.path.join()`处理路径分隔符
- ✅ 已修复：添加了跨平台路径处理
- ✅ 已修复：创建了路径工具模块

**快速修复步骤**:
1. 运行Windows路径修复脚本：
   ```bash
   python fix_windows_paths.py
   ```
2. 或手动确保文件位置正确：
   - ATX.apk → assets/apk/ATX.apk
   - 灰阶图片 → assets/images/
   - 配置文件 → config/

### 5. UIAutomator2服务冲突问题

**问题描述**: 出现UIAutomator2服务冲突错误，如：
```
java.lang.IllegalStateException: UiAutomationService already registered!
java.lang.IllegalStateException: UiAutomation not connected
```

**原因**:
- UIAutomator2服务在设备上只能有一个实例运行
- 多个客户端同时连接或连接未正确断开
- 程序异常退出时没有正确清理连接
- 设备上有其他工具正在使用UIAutomator2

**解决方案**:
- ✅ 已修复：添加了服务冲突检测和自动修复
- ✅ 已修复：改进了连接管理和资源清理
- ✅ 已修复：提供了ADB替代方案

**手动修复步骤**:
1. 运行UIAutomator2修复工具：
   ```bash
   python -c "from src.utils.uiautomator_fix import fix_uiautomator2_issues, print_fix_result; print_fix_result(fix_uiautomator2_issues())"
   ```

2. 或手动清理服务：
   ```bash
   adb shell pkill -9 -f uiautomator
   adb shell am force-stop com.github.uiautomator
   adb shell settings put secure enabled_accessibility_services ''
   ```

3. 重启设备（如果问题持续）

**程序行为**:
- 如果UI自动化失败，程序会自动使用ADB替代方案
- 不会影响测试的核心功能（数据收集和分析）
- 用户会看到友好的提示信息而不是技术错误

### 6. 数据收集问题

**问题描述**: 出现"无法获取到亮度值"错误，如：
```
ERROR - 获取当前数据失败: 无法获取到亮度值，请确认设备状态...
```

**可能原因**:
- 设备不是VIVO品牌或系统版本不支持VivoBrightnessPolicy
- logcat权限不足或命令执行失败
- 设备日志中没有相关亮度策略信息
- 正则表达式匹配问题

**解决方案**:
- ✅ 已添加：独立的数据收集诊断工具
- ✅ 已添加：详细的错误分析和调试信息
- ✅ 已添加：多种测试和验证方法

**诊断步骤**:
1. 运行简化版数据收集诊断工具（推荐）：
   ```bash
   python diagnose_data_collection.py
   ```
   或在Windows上双击：`diagnose_data.bat`

2. 运行完整版诊断工具：
   ```bash
   python src/data/log_collector.py
   ```

3. 运行快速测试：
   ```bash
   python test_data_collection.py
   ```

4. 在主程序中诊断：
   - 启动主程序
   - 选择菜单选项 `99: 数据收集诊断`

5. 手动触发亮度变化：
   - 在设备上手动调整亮度滑块
   - 开启/关闭自动亮度
   - 切换不同的显示模式

**常见解决方法**:
- **非VIVO设备**: 修改日志过滤关键词，查找其他品牌的亮度策略日志
- **权限问题**: 确保ADB有足够权限访问系统日志
- **系统版本**: 某些Android版本可能不输出详细的亮度策略日志
- **设备状态**: 确保设备处于活跃状态，屏幕亮起

### 7. 程序崩溃问题

**问题描述**: 程序在运行过程中意外退出

**解决方案**:
- ✅ 已修复：改进了错误处理机制
- ✅ 已修复：设备初始化失败时程序不会崩溃
- ✅ 已修复：添加了测试前检查功能

## 使用诊断工具

### 运行系统诊断
```bash
python run_diagnostic.py
```

诊断工具会检查：
- Python环境
- ADB工具状态
- 设备连接情况
- 依赖包安装
- 文件权限

### 诊断结果解读

#### ✅ 正常状态
- 所有检查项都显示绿色勾号
- 可以正常运行测试程序

#### ⚠️ 警告状态
- 黄色警告图标
- 功能可能受限，但程序可以运行
- 建议按提示解决问题

#### ❌ 错误状态
- 红色错误图标
- 必须解决才能正常使用
- 按照提示进行修复

## 环境配置指南

### Windows系统

1. **安装Python 3.8+**
   - 从python.org下载并安装
   - 安装时勾选"Add Python to PATH"

2. **安装ADB工具**
   ```bash
   # 方法1: 下载Android SDK Platform Tools
   # 下载地址: https://developer.android.com/studio/releases/platform-tools

   # 方法2: 使用包管理器（如果有）
   choco install adb  # 使用Chocolatey
   ```

3. **修复Windows路径问题**
   ```bash
   # 运行路径修复脚本
   python fix_windows_paths.py

   # 或使用批处理文件
   run_diagnostic.bat
   ```

4. **安装项目依赖**
   ```bash
   pip install -r requirements.txt
   ```

5. **使用Windows批处理脚本**
   - 双击 `run_diagnostic.bat` 进行系统诊断
   - 双击 `run_grey_test.bat` 启动测试程序

### Linux/macOS系统

1. **安装ADB工具**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install android-tools-adb
   
   # macOS
   brew install android-platform-tools
   ```

2. **安装项目依赖**
   ```bash
   pip3 install -r requirements.txt
   ```

## 设备配置指南

### Android设备设置

1. **开启开发者选项**
   - 设置 → 关于手机
   - 连续点击"版本号"7次
   - 输入锁屏密码（如果有）

2. **开启USB调试**
   - 设置 → 开发者选项
   - 开启"USB调试"
   - 开启"USB安装"（可选）

3. **连接设备**
   - 使用原装USB线连接设备和电脑
   - 在设备上选择"传输文件"模式
   - 允许USB调试权限

### 设备兼容性

#### 支持的设备
- VIVO品牌Android设备
- 支持LTPO或LTPS显示技术
- Android 7.0+

#### 已测试设备
- V2408A (LTPO)
- I2408 (LTPS)
- V2415A (LTPO)
- V2453A (LTPS)
- V2454A (LTPS)

## 性能优化建议

### 提高测试稳定性
1. 使用质量好的USB线
2. 确保设备电量充足
3. 关闭设备上的省电模式
4. 测试期间不要操作设备

### 提高测试速度
1. 关闭不必要的后台应用
2. 使用USB 3.0接口
3. 确保电脑性能充足

## 日志分析

### 日志文件位置
- 实时日志：`output/logs/latest.log`
- 历史日志：`output/logs/app_时间戳.log`

### 常见日志信息

#### 正常启动
```
2025-08-06 21:55:37,585 - INFO - 程序启动 - 日志文件: output/logs/app_2025-08-06_21-55-37.log
2025-08-06 21:55:37,585 - INFO - 开始初始化测试环境...
2025-08-06 21:55:37,585 - INFO - 设备连接正常
```

#### 设备连接问题
```
2025-08-06 21:55:37,769 - ERROR - 未检测到已连接的设备，请检查：
2025-08-06 21:55:37,769 - ERROR - 1. 设备是否通过USB连接到电脑
2025-08-06 21:55:37,769 - ERROR - 2. 设备是否开启了USB调试
```

#### 权限问题
```
2025-08-06 21:55:37,769 - WARNING - 未能获取root权限，某些功能可能受限
```

## 联系支持

如果按照本指南仍无法解决问题：

1. **收集信息**
   - 运行`python run_diagnostic.py`
   - 保存诊断结果
   - 收集相关日志文件

2. **描述问题**
   - 详细描述问题现象
   - 提供错误信息截图
   - 说明设备型号和系统版本

3. **提供环境信息**
   - 操作系统版本
   - Python版本
   - 项目版本

## 更新日志

### v2.0.0 修复内容
- ✅ 修复日志重复输出问题
- ✅ 改进设备连接检测
- ✅ 修复UTF-8编码错误
- ✅ 增强错误处理机制
- ✅ 添加系统诊断工具
- ✅ 改进用户体验
