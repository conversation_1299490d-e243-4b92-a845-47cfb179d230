import random
import time
from collections import Counter

from ADB import ADB
from ExADB import ExADB


class LtpoTestLogic:
    def __init__(self, get_log_data, desktop_test):
        self.get_log_data = get_log_data
        self.adb = ADB()
        self.ex_adb = ExADB(desktop_test=desktop_test)
        self.desktop_test = desktop_test

    def get_fps_value_most(self):
        fps_arr = []
        for i in range(5):
            fps_arr.append(self.adb.get_ltpo_fps())
            time.sleep(0.5)
        # 统计fps_arr中出现次数最多的fps
        fps_count = Counter(fps_arr)
        most_fps = fps_count.most_common(1)[0][0]
        return most_fps

    def get_fps_value(self):
        return self.get_fps_value_most()

    def check_grey_scale_strategy(self):
        """ 判断是否存在灰阶策略 """
        self.adb.switch_brightness_255(1)
        self.ex_adb.open_system_grey_image(index=00)
        time.sleep(4)
        fps_item1 = self.get_fps_value()
        self.ex_adb.open_system_grey_image(index=64)
        time.sleep(4)
        fps_item2 = self.get_fps_value()
        print("黑色: " + str(fps_item1) + "\t fps")
        print("白色: " + str(fps_item2) + "\t fps")
        if fps_item1 == fps_item2:
            return False
        return True

    def brightness_gradient_test(self):
        """ 灰阶亮度门限 """
        fps_arr = []
        self.ex_adb.open_system_grey_image(index=0)
        # 遍历所有可能的亮度值
        for brightness in range(1, 256):
            self.adb.switch_brightness_255(brightness)
            if brightness % 10 == 0:
                self.adb.reset_screen_policy()
            time.sleep(2)  # 等待设备响应亮度变化
            current_fps = self.get_fps_value()
            nit_value = self.get_log_data.get_nit_value()

            # 记录当前亮度下的NIT值和帧率
            fps_arr.append({"nit": nit_value, "fps": current_fps, "brightness": brightness})
            print("当前亮度：", {"nit": nit_value, "fps": current_fps, "brightness": brightness})

            # 如果帧率降到1fps，则停止测试
            if int(current_fps) <= 1:
                break

        return fps_arr

    def analyze_fps_gradients(self, fps_data):
        """ 分析帧率梯度 """
        gradients = []
        previous_fps = int(fps_data[0]["fps"])  # 初始化为第一个元素的fps值

        # 将第一个元素直接添加到结果列表中
        gradients.append(fps_data[0])

        # 遍历fps_data列表，从第二个元素开始比较相邻元素之间的帧率差异
        for i in range(1, len(fps_data)):
            current_fps = int(fps_data[i]["fps"])
            if current_fps != previous_fps:  # 如果存在帧率差异
                gradients.append(fps_data[i])
                previous_fps = current_fps  # 更新previous_fps为当前的fps值

        return gradients

    def grey_scale_data_treating(self, data):
        """ 数据处理，计算每个item的range参数 """
        if not data:
            return None
        s_value = 0  # 初始亮度值
        for item in data:
            e_value = item.get("brightness", 0)  # 获取当前亮度，默认为0
            # 生成范围随机值
            if s_value < e_value:
                item_range = random.randint(int(s_value), int(e_value))
            else:
                item_range = random.randint(int(e_value), int(s_value))
            # 确保range不为0
            if item_range == 0:
                item_range = 1
            item['range'] = item_range
            # 更新前一个亮度值为当前值
            s_value = e_value
        return data

    def gray_scale_test_value(self, item_dict):
        """ TODO 弃用二分查找，重构过程中发现，ltpo灰阶策略，也有1，5，10的变帧，选用0-64循环完成测试 """
        """ 二分查找灰阶临界点 """
        # 当fps为零时，直接返回0，无需判断
        if item_dict.get("fps") == '1' or item_dict.get("nit") == 1:
            return 0
        brightness_num = item_dict.get("range")
        self.adb.switch_brightness_255(brightness_num)
        self.ex_adb.open_system_grey_image(index=0)
        # 重置屏幕策略
        # self.Adb..reset_screen_policy()
        time.sleep(5)  # 等待确保fps值稳定
        # 开始执行二分查找
        low = 0
        high = 64  # 图片范围是0-64，所以这里的上界设为63
        fps_s = self.get_fps_value()  # 开始之前获取一次fps作为参考
        critical_index = 0  # 用于记录变帧临界点

        # 二分查找的主循环
        while low <= high:
            mid = (low + high) // 2
            self.ex_adb.open_system_grey_image(index=mid)
            # 重置屏幕策略
            # self.Adb..reset_screen_policy()
            time.sleep(5)  # 等待确保fps值稳定
            fps_c = self.get_fps_value()
            print("当前帧率: " + str(fps_c))
            print("检查的图片编号: ", mid)

            if fps_s != fps_c:  # 如果当前帧率不等于初始帧率，移动上界到中位数左侧
                high = mid - 1
                critical_index = mid
            else:  # 否则，移动下界到中位数右侧
                low = mid + 1


        if critical_index == 0:
            return 0
        else:
            return critical_index * 4  # 返回最初变化帧率在列表中的索引乘以4

    def new_gray_scale_test_value(self, item_dict):
        # 当fps为1时，直接返回0，无需判断
        # if item_dict.get("fps") == '1' or item_dict.get("nit") == 1:
        #     return 0
        brightness_num = item_dict.get("range")
        self.adb.switch_brightness_255(brightness_num)
        self.ex_adb.open_system_grey_image(index=0)

        # 重置屏幕策略（同时防止熄屏）
        self.adb.reset_screen_policy()

        time.sleep(5)  # 等待确保fps值稳定
        res_data = []
        # res_data = [{'index': 0, 'fps': '10'}, {'index': 1, 'fps': '10'}, {'index': 2, 'fps': '10'}, {'index': 3, 'fps': '10'}, {'index': 4, 'fps': '10'}, {'index': 5, 'fps': '10'}, {'index': 6, 'fps': '10'}, {'index': 7, 'fps': '10'}, {'index': 8, 'fps': '5'}, {'index': 9, 'fps': '5'}, {'index': 10, 'fps': '5'}, {'index': 11, 'fps': '5'}, {'index': 12, 'fps': '1'}, {'index': 13, 'fps': '1'}]
        # 图片范围是0-64，所以这里的上界设为64
        for i in range(0, 64):
            self.ex_adb.open_system_grey_image(index=i)
            time.sleep(3)
            fps_c = self.get_fps_value()
            print({'index': i, 'fps': fps_c})
            res_data.append(
                {'index': i, 'fps': fps_c}
            )
            if fps_c == 1 or fps_c == '1':
                break
        # 数据处理
        new_res_data = self.analyze_fps_gradients(res_data)
        # TODO 脏数据校验 开启后无法验证复杂性的灰阶策略
        # const_fps = new_res_data[0].get("fps")
        # for item in new_res_data:
        #     if int(item.get("fps")) > int(const_fps):
        #         item["fps"] = const_fps
        # new_res_data = self.analyze_fps_gradients(new_res_data)

        return new_res_data

    def gray_scale_threshold_test(self, data):
        """ 灰阶门限测试 """
        fps_dict = self.grey_scale_data_treating(data)
        # 开始灰阶测试
        res_arr = []
        for item in fps_dict:
            # grey_value = self.gray_scale_test_value(fps_dict[key])
            # res_arr.append({'fps': key, 'grey_value': grey_value, 'index': grey_value / 4})
            grey_value_arr = self.new_gray_scale_test_value(item)
            res_arr.append({'fps': item.get('fps'), 'grey_value_arr': grey_value_arr})
        return res_arr
