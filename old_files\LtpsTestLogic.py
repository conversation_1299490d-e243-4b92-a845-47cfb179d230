import time

from ADB import ADB
from ExADB import ExADB


class LtpsTestLogic:
    """ ltps测试逻辑 """
    def __init__(self, get_log_data, desktop_test):
        self.adb = ADB()
        self.ex_adb =ExADB(desktop_test=desktop_test)
        self.get_log_data = get_log_data
        self.desktop_test = desktop_test

    def check_grey_scale_strategy(self):
        self.ex_adb.open_system_grey_image(index=00)
        self.adb.switch_brightness_255(1)
        # 重置屏幕策略
        self.adb.reset_screen_policy()
        time.sleep(5)
        fps_item1 = self.get_log_data.get_fps_value()
        self.ex_adb.open_system_grey_image(index=64)
        # 重置屏幕策略
        self.adb.reset_screen_policy()
        time.sleep(5)
        fps_item2 = self.get_log_data.get_fps_value()
        print("黑色: " + str(fps_item1) + "\t fps")
        print("白色: " + str(fps_item2) + "\t fps")
        self.adb.page_left_to()
        if fps_item1 == fps_item2 and fps_item1 == 60:
            return False
        return True

    def brightness_threshold_test(self, image="BLACK"):
        """
        测试亮度门限 LTPS
        先用白色，在从最低亮度往最高亮度找，找到变帧的亮度档位，
        再用黑色，续着白色的亮度，找到变帧的亮度档位，使用黑色找的时候，白色和黑色变帧档位后的帧率应该一致
        白色变帧等于60，那黑色变帧也应该是60
        :param image: WHITE / BLACK string
        :return:
        """
        if image == "BLACK":
            index = 0
        else:
            index = 64
        print("打开灰阶测试图片...")
        self.ex_adb.open_system_grey_image(index=index)
        print("阻塞进程等待策略生效...")
        self.adb.switch_brightness_255(1)
        time.sleep(2)
        # 重置屏幕策略
        self.adb.reset_screen_policy()
        time.sleep(5)
        # 低亮帧率
        expected_fps = self.get_log_data.get_fps_value()
        self.adb.switch_brightness_255(255)
        self.adb.reset_screen_policy()
        time.sleep(5)
        # 目标帧率
        changed_fps = self.get_log_data.get_fps_value()
        brightness_value = self.binary_search_critical_brightness(low=0, high=255, expected_fps=expected_fps, changed_fps=changed_fps)

        res_arr = []
        # 获取fps的临界点
        brightness_value = brightness_value - 1
        self.adb.switch_brightness_255(brightness_value)
        self.adb.reset_screen_policy()
        time.sleep(5)
        res_arr.append({'nit': self.get_log_data.get_nit_value(), 'fps': self.get_log_data.get_fps_value(), 'brightness': brightness_value})
        # 获取fps的临界点
        brightness_value = brightness_value + 1
        self.adb.switch_brightness_255(brightness_value)
        self.adb.reset_screen_policy()
        time.sleep(5)
        res_arr.append({'nit': self.get_log_data.get_nit_value(), 'fps': self.get_log_data.get_fps_value(), 'brightness': brightness_value})
        return res_arr

    def binary_search_critical_brightness(self, low, high, expected_fps, changed_fps=60, delay=8):
        """二分查找导致FPS变化的亮度临界值（优化版）

        :param low: 亮度搜索下限
        :param high: 亮度搜索上限
        :param expected_fps: 正常状态期望FPS值
        :param changed_fps: 变帧后的目标FPS值（默认60）
        :param delay: 亮度稳定等待时间（秒）
        :return: 临界亮度值（未找到返回-1）
        """

        def set_and_measure(brightness):
            """封装亮度设置与FPS测量流程"""
            self.adb.switch_brightness_255(brightness)
            self.adb.reset_screen_policy()
            time.sleep(delay)
            return self.get_log_data.get_fps_value()

        while low <= high:
            mid = (low + high) // 2
            current_fps = set_and_measure(mid)

            if current_fps == changed_fps:
                if mid == low:  # 处理边界情况：找到最低有效亮度
                    return mid

                # 验证前一个亮度是否保持原FPS
                prev_fps = set_and_measure(mid - 1)
                if prev_fps == expected_fps:
                    return mid
                high = mid - 1  # 临界点在左侧区间
            else:
                low = mid + 1  # 临界点在右侧区间

        return -1

    def findMedianBrightness(self, data):
        """ 寻找灰阶门限所需的亮度值 """
        i = 0
        previous_value = 0
        for item in data:
            if i == 0:
                item['range'] = int(int(item.get('brightness')) / 2)
            else:
                item['range'] = int(item.get('brightness')) - int(((int(item.get('brightness')) - previous_value) / 2))
            previous_value = item.get('brightness')
            i += 1
        return data

    def gray_scale_test_value(self, item_dict):
        """ 二分查找灰阶临界点 """
        # 当fps为零时，直接返回0，无需判断
        if item_dict.get("fps") == '1' or item_dict.get("nit") == 1:
            return 0
        brightness_num = item_dict.get("range")
        self.adb.switch_brightness_255(int(brightness_num))
        self.ex_adb.open_system_grey_image(index=0)
        # 重置屏幕策略
        self.adb.reset_screen_policy()
        time.sleep(5)  # 等待确保fps值稳定
        # 开始执行二分查找
        low = 0
        high = 64  # 你的图片范围是0-65，所以这里的上界设为64
        fps_s = self.get_log_data.get_fps_value()  # 开始之前获取一次fps作为参考
        critical_index = 0  # 用于记录变帧临界点
        fps_c = 0
        # 二分查找的主循环
        while low <= high:
            mid = (low + high) // 2
            self.ex_adb.open_system_grey_image(index=mid)
            # 重置屏幕策略
            self.adb.reset_screen_policy()
            time.sleep(5)  # 等待确保fps值稳定
            fps_c = self.get_log_data.get_fps_value()
            print("当前帧率: " + str(fps_c))
            print("检查的图片编号: ", mid)

            if fps_s != fps_c:  # 如果当前帧率不等于初始帧率，移动上界到中位数左侧
                high = mid - 1
                critical_index = mid
            else:  # 否则，移动下界到中位数右侧
                low = mid + 1
                if fps_c == 60:  # 如果达到了60帧，直接返回0
                    return 0

        if critical_index == 0:
            return fps_c, 0
        else:
            return fps_s, (critical_index - 1) * 4  # 返回最初变化帧率在列表中的索引乘以4

    def gray_scale_threshold_test(self, data):
        """ 灰阶门限测试 """
        # 寻找亮度中间值
        # 白色
        # 19=60  18=90
        # {'brightness': 18, 'fps': 90, 'nit': '24'}
        # 黑色
        # 87 = 90  88 = 60
        # {'brightness': 88, 'fps': 60, 'nit': '122'}
        # data = [{'brightness': 18, 'fps': 90, 'nit': '24'}, {'brightness': 88, 'fps': 60, 'nit': '122'}]
        fps_dict = self.findMedianBrightness(data)
        res_arr = []
        for item in fps_dict:
            fps, grey_value = self.gray_scale_test_value(item)
            item['fps'] = fps
            item['grey_value'] = grey_value
            item['index'] = grey_value / 4
            res_arr.append(item)
        return res_arr
