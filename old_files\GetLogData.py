import random
import re
import subprocess
import sys
import threading
import time
from queue import Queue
from ADB import ADB
from SaveFile import SaveFile


class GetLogData:
    def __init__(self):
        self.log_queue = Queue()
        self.nit_current_value = 0
        self.adb = ADB()

    def extract_nit_value(self, log_line):
        """ 处理字符串获取其中的nit值 """
        match = re.search(r'target nit (\d+)', log_line)
        if match:
            return match.group(1)  # 返回数字字符串
        return None

    def adb_logcat_nit(self):
        """ 从日志文件中持续读取nit变化 """
        cmd = ["adb", "shell", "logcat", "|", "grep", "VivoBrightnessPolicy"]
        # cmd = ["adb", "shell", "logcat", "|", "grep", "_V_pie_display"]
        with subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True) as process:
            for line in process.stdout:
                if "Get target percent" in line:
                    # if "POWER_ID_BRIGHTNESS" in line:
                    self.log_queue.put(line)  # 将捕获的日志行放入队列中

    def start_threads(self):
        """ 启动捕获线程 """
        # 创建并启动线程，使用self.adb_logcat_nit作为目标函数
        adb_logcat_nit_thread = threading.Thread(target=self.adb_logcat_nit)
        adb_logcat_nit_thread.daemon = True
        adb_logcat_nit_thread.setDaemon(True)
        adb_logcat_nit_thread.start()

    def get_nit_value(self):
        """ 获取nit数据 """
        last_nit_data = None
        while not self.log_queue.empty():
            last_nit_data = self.log_queue.get()
            # print("get:" + last_nit_data.strip())

        if last_nit_data:
            nit_value = self.extract_nit_value(last_nit_data.strip())
            if nit_value:
                self.nit_current_value = nit_value

        if 0 == self.nit_current_value:
            # 抛出错误并中断程序
            raise ValueError("无法获取到亮度值，请确认设备状态...")  # 抛出 ValueError 异常

        return self.nit_current_value

    def get_fps_value(self):
        """ 获取fps数据 """
        return self.adb.get_fps(True)
