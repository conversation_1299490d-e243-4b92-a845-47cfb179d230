import sys
import logging
import LtpsTest
import LtpoTest
from ADB import ADB
from datetime import datetime
import os
import shutil
import builtins

# 保存原始的print函数
original_print = builtins.print
# 全局变量用于存储日志print函数
log_print = None


def setup_logging():
    global log_print
    # 创建日志目录（如果不存在）
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 生成包含日期和时间的日志文件名，确保唯一性
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    log_file = os.path.join(log_dir, f"app_{timestamp}.log")

    # 创建日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 清除现有的处理器（避免重复日志）
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)

    # 创建文件处理器，将日志写入文件
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 创建控制台处理器，将日志输出到控制台
    console_handler = logging.StreamHandler(stream=sys.__stdout__)
    console_handler.setLevel(logging.INFO)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 将处理器添加到记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 创建最新日志的软链接或副本
    latest_log = os.path.join(log_dir, "latest.log")
    try:
        # 尝试删除已存在的latest.log
        if os.path.exists(latest_log):
            os.remove(latest_log)

        # 在Windows上创建副本，在Unix/Linux上创建软链接
        if os.name == 'nt':  # Windows
            shutil.copy2(log_file, latest_log)
        else:  # Unix/Linux
            os.symlink(os.path.basename(log_file), latest_log)
    except Exception as e:
        logger.warning(f"创建最新日志链接失败: {e}")

    # 记录程序启动信息
    logger.info(f"程序启动 - 日志文件: {log_file}")
    logger.info(f"系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)

    # 全局替换print函数
    def global_print_override(*args, sep=' ', end='\n', file=None, flush=False):
        message = sep.join(map(str, args))
        # 只通过logging记录，不重复输出
        logger.info(message)
        # 如果指定了文件，仍然写入到该文件
        if file is not None and file is not sys.stdout and file is not sys.stderr:
            original_print(*args, sep=sep, end=end, file=file, flush=flush)

    # 保存到全局变量
    log_print = global_print_override

    # 替换内置的print函数，这样所有模块都会使用这个版本
    builtins.print = global_print_override

    return log_file


if __name__ == '__main__':
    try:
        # 设置日志记录
        current_log_file = setup_logging()

        # 记录脚本执行环境信息
        logging.info(f"Python版本: {sys.version}")
        logging.info(f"脚本路径: {os.path.abspath(__file__)}")

        LtpoTestModel = LtpoTest
        LtpsTestModel = LtpsTest
        Adb = ADB()
        if Adb.get_auto_brightness_status():
            print("检测到自动亮度开启，现在为您关闭...")
            Adb.disable_auto_brightness()
            print("自动亮度已关闭")
        # 默认开启灰阶校验
        isInspected = True
        while True:
            print("请输入命令:")
            print("1: LtpoTest-LTPO灰阶测试")
            print("2: LtpsTest-LTPS灰阶测试")
            print("3: 桌面LtpoTest-LTPO灰阶测试")
            print("4: 桌面LtpsTest-LTPS灰阶测试")
            print("5: 开启深色模式")
            print("6: 关闭深色模式")
            print("7: 切换智能帧率")
            print("8: 切换标准帧率")
            print("9: 切换高帧率")
            print("00: 关闭灰阶校验")
            print("0: 退出")

            # 对于input函数，我们需要使用原始的stdin，并记录用户输入
            # 临时恢复原始print以显示提示符
            builtins.print = original_print
            user_input = input().strip()
            # 恢复日志print
            builtins.print = log_print
            print(f"用户输入: {user_input}")

            if user_input == '1':
                print("start-开始-LtpoTest-LTPO灰阶测试")
                LtpoTestModel.main(isInspected=isInspected)
                print(f"测试完成，日志已保存至: {current_log_file}")
                builtins.print = original_print
                input("按回车键退出程序...")
                break
            elif user_input == '2':
                print("start-开始-LtpsTest-LTPS灰阶测试")
                LtpsTestModel.main(isInspected=isInspected)
                print(f"测试完成，日志已保存至: {current_log_file}")
                builtins.print = original_print
                input("按回车键退出程序...")
                break
            elif user_input == '3':
                print("start-开始-桌面LtpoTest-LTPO灰阶测试")
                LtpoTestModel.main(desktop_test=True, isInspected=isInspected)
                print(f"测试完成，日志已保存至: {current_log_file}")
                builtins.print = original_print
                input("按回车键退出程序...")
                break
            elif user_input == '4':
                print("start-开始-桌面LtpsTest-LTPS灰阶测试")
                LtpsTestModel.main(desktop_test=True, isInspected=isInspected)
                print(f"测试完成，日志已保存至: {current_log_file}")
                builtins.print = original_print
                input("按回车键退出程序...")
                break
            elif user_input == '5':
                print("已开启深色模式")
                Adb.start_dark_mode()
            elif user_input == '6':
                print("已关闭深色模式")
                Adb.stop_dark_mode()
            elif user_input == '7':
                print("已切换智能帧率")
                Adb.start_lcm_fps_1()
            elif user_input == '8':
                print("已切换标准帧率")
                Adb.start_lcm_fps_60()
            elif user_input == '9':
                print("已切换高帧率")
                Adb.start_lcm_fps_120()
            elif user_input == '00':
                print("已关闭灰阶校验")
                isInspected = False
            elif user_input == '0':
                print("退出程序")
                print(f"日志已保存至: {current_log_file}")
                break
            else:
                print("未知命令，请重新输入。")

    except Exception as e:
        # 确保异常也被记录到日志中
        logging.error(f"发生错误: {e}", exc_info=True)
        # 恢复原始print以便显示错误信息
        builtins.print = original_print
        print(f"发生错误: {e}")
        print(f"详细错误信息已记录到日志文件: {current_log_file}")
        sys.exit(1)
    finally:
        # 恢复原始的print函数
        builtins.print = original_print
