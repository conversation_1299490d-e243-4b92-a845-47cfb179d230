import sys

from GetLogData import GetLogData
from LtpoTestLogic import LtpoTestLogic
from ADB import ADB
from SaveFile import SaveFile


def main(desktop_test=False, isInspected=True):
    get_log_data = GetLogData()
    get_log_data.start_threads()
    adb = ADB()
    logic = LtpoTestLogic(get_log_data=get_log_data, desktop_test=desktop_test)
    print("检查是否安装ATX...")
    is_install = adb.is_app_installed("uiautomator")
    if not is_install:
        # 安装ATX
        print("未安装ATX\n正在安装ATX...")
        adb.install_atx("ATX.apk")

    try:
        print("获取root权限...")
        adb.get_root()
        print("唤醒屏幕...")
        adb.wake_up_screen()
        print("上传测试文件...")
        adb.upload_file()

        # 判断是否跳过校验
        if isInspected:
            # 1.判断是否存在灰阶
            if not logic.check_grey_scale_strategy():
                sys.exit("该设备不存在灰阶策略")
            print("该设备存在灰阶策略，开始进行灰阶亮度门限测试...")

        # 2.灰阶亮度门限测试
        threshold_data = logic.brightness_gradient_test()
        # threshold_data = [{'nit': '3', 'fps': '60', 'brightness': 1}, {'nit': '3', 'fps': '60', 'brightness': 2}, {'nit': '4', 'fps': '60', 'brightness': 3}, {'nit': '6', 'fps': '60', 'brightness': 4}, {'nit': '8', 'fps': '10', 'brightness': 5}, {'nit': '9', 'fps': '10', 'brightness': 6}, {'nit': '10', 'fps': '10', 'brightness': 7}, {'nit': '11', 'fps': '10', 'brightness': 8}, {'nit': '13', 'fps': '10', 'brightness': 9}, {'nit': '14', 'fps': '10', 'brightness': 10}, {'nit': '15', 'fps': '10', 'brightness': 11}, {'nit': '17', 'fps': '10', 'brightness': 12}, {'nit': '18', 'fps': '10', 'brightness': 13}, {'nit': '19', 'fps': '10', 'brightness': 14}, {'nit': '20', 'fps': '10', 'brightness': 15}, {'nit': '22', 'fps': '10', 'brightness': 16}, {'nit': '23', 'fps': '10', 'brightness': 17}, {'nit': '24', 'fps': '10', 'brightness': 18}, {'nit': '26', 'fps': '10', 'brightness': 19}, {'nit': '27', 'fps': '10', 'brightness': 20}, {'nit': '28', 'fps': '10', 'brightness': 21}, {'nit': '30', 'fps': '10', 'brightness': 22}, {'nit': '31', 'fps': '10', 'brightness': 23}, {'nit': '32', 'fps': '10', 'brightness': 24}, {'nit': '34', 'fps': '10', 'brightness': 25}, {'nit': '35', 'fps': '10', 'brightness': 26}, {'nit': '35', 'fps': '10', 'brightness': 27}, {'nit': '36', 'fps': '10', 'brightness': 28}, {'nit': '37', 'fps': '10', 'brightness': 29}, {'nit': '37', 'fps': '10', 'brightness': 30}, {'nit': '38', 'fps': '10', 'brightness': 31}, {'nit': '39', 'fps': '10', 'brightness': 32}, {'nit': '39', 'fps': '10', 'brightness': 33}, {'nit': '40', 'fps': '10', 'brightness': 34}, {'nit': '41', 'fps': '10', 'brightness': 35}, {'nit': '41', 'fps': '10', 'brightness': 36}, {'nit': '42', 'fps': '10', 'brightness': 37}, {'nit': '43', 'fps': '10', 'brightness': 38}, {'nit': '43', 'fps': '10', 'brightness': 39}, {'nit': '44', 'fps': '10', 'brightness': 40}, {'nit': '45', 'fps': '10', 'brightness': 41}, {'nit': '45', 'fps': '10', 'brightness': 42}, {'nit': '46', 'fps': '10', 'brightness': 43}, {'nit': '47', 'fps': '10', 'brightness': 44}, {'nit': '47', 'fps': '10', 'brightness': 45}, {'nit': '48', 'fps': '10', 'brightness': 46}, {'nit': '49', 'fps': '10', 'brightness': 47}, {'nit': '50', 'fps': '1', 'brightness': 48}]
        print("亮度门限数据：")
        print(threshold_data)
        threshold_data = logic.analyze_fps_gradients(threshold_data)
        # threshold_data = [{'nit': '3', 'fps': '60', 'brightness': 1}, {'nit': '8', 'fps': '10', 'brightness': 5}, {'nit': '50', 'fps': '1', 'brightness': 48}]
        print("分析后数据：")
        print(threshold_data)
        # 3.灰阶门限测试
        gray_data = logic.gray_scale_threshold_test(threshold_data)
        # gray_data = [{'fps': '60', 'grey_value_arr': [{'index': 0, 'fps': '60'}, {'index': 13, 'fps': '10'}]}, {'fps': '10', 'grey_value_arr': [{'index': 0, 'fps': '60'}, {'index': 13, 'fps': '10'}]}, {'fps': '1', 'grey_value_arr': [{'index': 0, 'fps': '10'}, {'index': 1, 'fps': '60'}, {'index': 6, 'fps': '10'}]}]
        print("灰阶门限")
        print(gray_data)
        """ 输出结果文件 """
        save_file = SaveFile(brightness_data=threshold_data, grey_scale=gray_data)
        # 合并数据
        merged_data = save_file.merge_data()
        print("合并数据")
        print(merged_data)
        formatted_data = save_file.format_data(merged_data)
        save_file.save_to_file(result_data=formatted_data, title="LTPO灰阶测试结果")

    except Exception as e:
        print(e)
        input("按回车键退出程序...")
        sys.exit()


if __name__ == '__main__':
    main()
