"""
UI自动化模块
提供设备UI自动化操作功能
"""

import time
import uiautomator2 as u2
from typing import Optional, Tuple
from src.core.adb_controller import ADBController
from src.utils.constants import GREY_IMAGE_PREFIX, GREY_IMAGE_SUFFIX
from src.utils.logger import get_logger


class UIAutomation:
    """UI自动化控制器"""

    def __init__(self):
        self.device = None
        self.adb = ADBController()
        self.logger = get_logger()
        self.is_connected = False
        self.connection_retries = 0
        self.max_retries = 3

    def connect(self) -> bool:
        """
        连接设备

        Returns:
            连接是否成功
        """
        if self.is_connected and self.device:
            return True

        # 清理可能存在的冲突连接
        self._cleanup_existing_connections()

        for attempt in range(self.max_retries):
            try:
                if self.logger:
                    self.logger.info(f"尝试连接UI自动化服务 (第{attempt + 1}次)")

                # 重置UIAutomator2服务
                if attempt > 0:
                    self._reset_uiautomator_service()

                self.device = u2.connect()

                # 验证连接是否正常
                if self._verify_connection():
                    self._setup_watchers()
                    self.is_connected = True
                    if self.logger:
                        self.logger.info("UI自动化连接成功")
                    return True
                else:
                    if self.logger:
                        self.logger.warning(f"连接验证失败，准备重试 (第{attempt + 1}次)")
                    self.device = None

            except Exception as e:
                if self.logger:
                    self.logger.warning(f"UI自动化连接失败 (第{attempt + 1}次): {e}")

                # 检查是否是服务冲突错误
                if "already registered" in str(e) or "UiAutomation not connected" in str(e):
                    if self.logger:
                        self.logger.info("检测到UIAutomator2服务冲突，尝试清理...")
                    self._force_cleanup_service()

                self.device = None
                time.sleep(2)  # 等待2秒后重试

        if self.logger:
            self.logger.error("UI自动化连接失败，已达到最大重试次数")
        return False
    
    def _cleanup_existing_connections(self) -> None:
        """清理可能存在的冲突连接"""
        try:
            if self.logger:
                self.logger.debug("清理可能存在的UIAutomator2连接...")

            # 停止可能运行的UIAutomator2服务
            self.adb.execute_command("adb shell am force-stop com.github.uiautomator")
            self.adb.execute_command("adb shell am force-stop com.github.uiautomator.test")

            # 清理UIAutomator2相关进程
            self.adb.execute_command("adb shell pkill -f uiautomator")

            time.sleep(1)

        except Exception as e:
            if self.logger:
                self.logger.debug(f"清理连接时出错: {e}")

    def _reset_uiautomator_service(self) -> None:
        """重置UIAutomator2服务"""
        try:
            if self.logger:
                self.logger.debug("重置UIAutomator2服务...")

            # 停止UIAutomator2服务
            self.adb.execute_command("adb shell am force-stop com.github.uiautomator")
            self.adb.execute_command("adb shell am force-stop com.github.uiautomator.test")

            # 清理accessibility服务
            self.adb.execute_command("adb shell settings put secure enabled_accessibility_services ''")

            time.sleep(2)

            # 重新启动UIAutomator2服务
            self.adb.execute_command("adb shell am instrument -w -r -e debug false com.github.uiautomator.test/androidx.test.runner.AndroidJUnitRunner")

            time.sleep(3)

        except Exception as e:
            if self.logger:
                self.logger.debug(f"重置UIAutomator2服务时出错: {e}")

    def _force_cleanup_service(self) -> None:
        """强制清理UIAutomator2服务"""
        try:
            if self.logger:
                self.logger.debug("强制清理UIAutomator2服务...")

            # 杀死所有相关进程
            self.adb.execute_command("adb shell pkill -9 -f uiautomator")
            self.adb.execute_command("adb shell pkill -9 -f com.github.uiautomator")

            # 清理accessibility服务注册
            self.adb.execute_command("adb shell settings put secure enabled_accessibility_services ''")

            # 重启accessibility服务
            self.adb.execute_command("adb shell service call accessibility 1")

            time.sleep(3)

        except Exception as e:
            if self.logger:
                self.logger.debug(f"强制清理服务时出错: {e}")

    def _verify_connection(self) -> bool:
        """验证连接是否正常"""
        try:
            if not self.device:
                return False

            # 尝试获取设备信息来验证连接
            info = self.device.info
            if info and 'displayWidth' in info:
                return True

            return False

        except Exception as e:
            if self.logger:
                self.logger.debug(f"连接验证失败: {e}")
            return False

    def _setup_watchers(self) -> None:
        """设置监听器"""
        if not self.device:
            return

        try:
            # 设置系统初始化监听器
            self.device.watcher.when("始终").click()
            self.device.watcher.when("仅此一次").click()
            self.device.watcher.when("允许").click()
            self.device.watcher.start()
        except Exception as e:
            if self.logger:
                self.logger.warning(f"设置监听器失败: {e}")
    
    def disconnect(self) -> None:
        """断开连接"""
        try:
            if self.device:
                # 停止监听器
                try:
                    self.device.watcher.stop()
                except:
                    pass

                # 清理连接
                self.device = None
                self.is_connected = False

                if self.logger:
                    self.logger.info("UI自动化连接已断开")

        except Exception as e:
            if self.logger:
                self.logger.warning(f"断开连接时出错: {e}")

    def get_device_info(self) -> Optional[dict]:
        """获取设备信息"""
        if not self.is_connected or not self.device:
            return None

        try:
            return self.device.info
        except Exception as e:
            if self.logger:
                self.logger.warning(f"获取设备信息失败: {e}")
            return None
    
    def get_screen_size(self) -> Optional[Tuple[int, int]]:
        """获取屏幕尺寸"""
        if self.device:
            return self.device.window_size()
        return None
    
    def _ensure_connected(self) -> bool:
        """确保连接正常"""
        if not self.is_connected or not self.device:
            if self.logger:
                self.logger.debug("UI自动化未连接，尝试重新连接...")
            return self.connect()
        return True

    def click_text(self, text: str, timeout: int = 10) -> bool:
        """
        点击指定文本

        Args:
            text: 要点击的文本
            timeout: 超时时间

        Returns:
            点击是否成功
        """
        if not self._ensure_connected():
            if self.logger:
                self.logger.warning(f"UI自动化未连接，无法点击文本: {text}")
            return False

        try:
            element = self.device(text=text)
            if element.wait(timeout=timeout):
                element.click()
                return True
        except Exception as e:
            if self.logger:
                self.logger.warning(f"点击文本失败: {text}, 错误: {e}")

            # 如果是连接相关错误，尝试重新连接
            if "UiAutomation not connected" in str(e):
                self.is_connected = False
                if self._ensure_connected():
                    return self.click_text(text, timeout)
        return False
    
    def click_description(self, description: str, timeout: int = 10) -> bool:
        """
        点击指定描述的元素
        
        Args:
            description: 元素描述
            timeout: 超时时间
            
        Returns:
            点击是否成功
        """
        if not self.device:
            return False
        
        try:
            element = self.device(description=description)
            if element.wait(timeout=timeout):
                element.click()
                return True
        except Exception as e:
            if self.logger:
                self.logger.warning(f"点击描述失败: {description}, 错误: {e}")
        return False
    
    def click_xpath(self, xpath: str, timeout: int = 10) -> bool:
        """
        使用XPath点击元素
        
        Args:
            xpath: XPath表达式
            timeout: 超时时间
            
        Returns:
            点击是否成功
        """
        if not self.device:
            return False
        
        try:
            element = self.device.xpath(xpath)
            if element.wait(timeout=timeout):
                element.click()
                return True
        except Exception as e:
            if self.logger:
                self.logger.warning(f"XPath点击失败: {xpath}, 错误: {e}")
        return False
    
    def is_text_exists(self, text: str, timeout: int = 5) -> bool:
        """
        检查文本是否存在

        Args:
            text: 要检查的文本
            timeout: 超时时间

        Returns:
            文本是否存在
        """
        if not self._ensure_connected():
            return False

        try:
            return self.device(text=text).wait(timeout=timeout)
        except Exception as e:
            if self.logger:
                self.logger.debug(f"检查文本存在性失败: {text}, 错误: {e}")
            return False
    
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5) -> bool:
        """
        滑动操作
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 滑动持续时间
            
        Returns:
            滑动是否成功
        """
        if not self.device:
            return False
        
        try:
            self.device.swipe(start_x, start_y, end_x, end_y, duration=duration)
            return True
        except Exception as e:
            if self.logger:
                self.logger.warning(f"滑动操作失败: {e}")
            return False
    
    def swipe_left(self, times: int = 1, speed: float = 0.5) -> bool:
        """向左滑动"""
        if not self.device:
            return False
        
        width, height = self.device.window_size()
        start_x = int(width * 0.8)
        end_x = int(width * 0.2)
        center_y = height // 2
        
        for _ in range(times):
            if not self.swipe(start_x, center_y, end_x, center_y, speed):
                return False
        return True
    
    def swipe_right(self, times: int = 1, speed: float = 0.5) -> bool:
        """向右滑动"""
        if not self.device:
            return False
        
        width, height = self.device.window_size()
        start_x = int(width * 0.2)
        end_x = int(width * 0.8)
        center_y = height // 2
        
        for _ in range(times):
            if not self.swipe(start_x, center_y, end_x, center_y, speed):
                return False
        return True
    
    def swipe_up(self, times: int = 1, speed: float = 0.5) -> bool:
        """向上滑动"""
        if not self.device:
            return False
        
        width, height = self.device.window_size()
        center_x = width // 2
        start_y = int(height * 0.8)
        end_y = int(height * 0.2)
        
        for _ in range(times):
            if not self.swipe(center_x, start_y, center_x, end_y, speed):
                return False
        return True
    
    def swipe_down(self, times: int = 1, speed: float = 0.5) -> bool:
        """向下滑动"""
        if not self.device:
            return False
        
        width, height = self.device.window_size()
        center_x = width // 2
        start_y = int(height * 0.2)
        end_y = int(height * 0.8)
        
        for _ in range(times):
            if not self.swipe(center_x, start_y, center_x, end_y, speed):
                return False
        return True


class SafeUIAutomation:
    """安全的UI自动化包装器"""

    def __init__(self):
        self.ui = None
        self.logger = get_logger()
        self.is_available = False

    def initialize(self) -> bool:
        """初始化UI自动化"""
        try:
            if self.logger:
                self.logger.info("初始化UI自动化服务...")

            self.ui = UIAutomation()
            self.is_available = self.ui.connect()

            if self.is_available:
                if self.logger:
                    self.logger.info("UI自动化服务初始化成功")
            else:
                if self.logger:
                    self.logger.warning("UI自动化服务初始化失败，将使用ADB替代方案")

            return self.is_available

        except Exception as e:
            if self.logger:
                self.logger.warning(f"UI自动化初始化异常: {e}")
            self.is_available = False
            return False

    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.ui:
                self.ui.disconnect()
                self.ui = None
            self.is_available = False
        except Exception as e:
            if self.logger:
                self.logger.debug(f"UI自动化清理时出错: {e}")

    def click_text(self, text: str, timeout: int = 10) -> bool:
        """安全的文本点击"""
        if not self.is_available or not self.ui:
            return False

        try:
            return self.ui.click_text(text, timeout)
        except Exception as e:
            if self.logger:
                self.logger.debug(f"安全文本点击失败: {e}")
            return False

    def is_text_exists(self, text: str, timeout: int = 5) -> bool:
        """安全的文本存在检查"""
        if not self.is_available or not self.ui:
            return False

        try:
            return self.ui.is_text_exists(text, timeout)
        except Exception as e:
            if self.logger:
                self.logger.debug(f"安全文本检查失败: {e}")
            return False


class GreyScaleImageController:
    """灰阶图片控制器"""

    def __init__(self, desktop_test: bool = False):
        self.adb = ADBController()
        self.ui = SafeUIAutomation()
        self.desktop_test = desktop_test
        self.logger = get_logger()
        self.ui_available = False

    def initialize(self) -> bool:
        """初始化控制器"""
        try:
            # 尝试初始化UI自动化
            self.ui_available = self.ui.initialize()

            if not self.ui_available:
                if self.logger:
                    self.logger.info("UI自动化不可用，将使用纯ADB方案")

            return True  # 即使UI自动化失败，控制器仍可工作

        except Exception as e:
            if self.logger:
                self.logger.error(f"灰阶图片控制器初始化失败: {e}")
            return False

    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.ui.cleanup()
        except Exception as e:
            if self.logger:
                self.logger.debug(f"灰阶图片控制器清理时出错: {e}")
    
    def open_grey_image(self, index: int) -> bool:
        """
        打开指定索引的灰阶图片

        Args:
            index: 灰阶图片索引 (0-64)

        Returns:
            打开是否成功
        """
        if not 0 <= index <= 64:
            if self.logger:
                self.logger.error(f"无效的灰阶图片索引: {index}")
            return False

        try:
            # 停止当前应用
            current_app = self.adb.get_current_activity()
            if current_app:
                self.adb.force_stop_app(current_app)

            # 生成文件名
            file_name = f"{GREY_IMAGE_PREFIX}{index:02d}"

            time.sleep(1)

            if self.desktop_test:
                return self._open_as_wallpaper(file_name)
            else:
                return self._open_in_gallery(file_name)

        except Exception as e:
            if self.logger:
                self.logger.error(f"打开灰阶图片失败: {e}")
            return False
    
    def _open_in_gallery(self, file_name: str) -> bool:
        """在相册中打开图片"""
        try:
            # Android设备路径始终使用正斜杠
            device_image_path = f"/sdcard/images/{file_name}.{GREY_IMAGE_SUFFIX}"
            command = (f'adb shell am start -a android.intent.action.VIEW '
                      f'-n com.vivo.gallery/com.android.gallery3d.app.Gallery '
                      f'-d "file://{device_image_path}" '
                      f'-t "image/*"')

            if self.logger:
                self.logger.debug(f"打开图片命令: {command}")

            self.adb.execute_command(command)
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"在相册中打开图片失败: {e}")
            return False
    
    def _open_as_wallpaper(self, file_name: str) -> bool:
        """设置为壁纸"""
        try:
            # Android设备路径始终使用正斜杠
            device_image_path = f"/sdcard/static_grey/{file_name}.{GREY_IMAGE_SUFFIX}"

            # 使用系统意图打开图片
            command = (f'adb shell am start -a android.intent.action.ATTACH_DATA '
                      f'-d file://{device_image_path} '
                      f'-t image/jpeg')

            if self.logger:
                self.logger.debug(f"设置壁纸命令: {command}")

            self.adb.execute_command(command)
            time.sleep(2)

            # 如果UI自动化可用，尝试处理对话框
            if self.ui_available:
                try:
                    # 处理选择器对话框
                    if self.ui.is_text_exists("设为默认选项。"):
                        self.ui.click_text("壁纸")
                        time.sleep(1)
                    elif self.ui.is_text_exists("始终"):
                        self.ui.click_text("壁纸")
                        time.sleep(1)

                    # 处理壁纸设置对话框
                    if self.ui.is_text_exists("设定壁纸"):
                        self.ui.click_text("设定壁纸")
                        time.sleep(1)

                    if self.ui.is_text_exists("主屏幕"):
                        self.ui.click_text("主屏幕")
                        time.sleep(2)

                except Exception as ui_error:
                    if self.logger:
                        self.logger.warning(f"UI自动化处理对话框失败，使用ADB替代: {ui_error}")
                    # 使用ADB模拟点击
                    self._handle_wallpaper_dialogs_with_adb()
            else:
                # 使用ADB替代方案
                self._handle_wallpaper_dialogs_with_adb()

            # 返回桌面
            self.adb.execute_command("adb shell input keyevent KEYCODE_HOME")
            time.sleep(1)

            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"设置壁纸失败: {e}")
            return False

    def _handle_wallpaper_dialogs_with_adb(self) -> None:
        """使用ADB处理壁纸设置对话框"""
        try:
            # 等待对话框出现
            time.sleep(2)

            # 模拟点击屏幕中央（通常是确认按钮的位置）
            self.adb.execute_command("adb shell input tap 540 960")  # 假设屏幕分辨率1080x1920
            time.sleep(1)

            # 再次点击确认
            self.adb.execute_command("adb shell input tap 540 1200")
            time.sleep(1)

        except Exception as e:
            if self.logger:
                self.logger.debug(f"ADB处理对话框失败: {e}")
