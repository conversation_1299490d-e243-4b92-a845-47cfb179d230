"""
安装脚本
用于安装灰阶测试工具
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "灰阶自动化测试工具"

# 读取requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return ['uiautomator2>=2.16.23']

setup(
    name="grey-scale-test",
    version="2.0.0",
    description="Android设备灰阶自动化测试工具",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="Grey Scale Test Team",
    author_email="",
    url="",
    packages=find_packages(),
    include_package_data=True,
    package_data={
        'src': ['**/*.py'],
        'config': ['*.json'],
        'assets': ['**/*'],
    },
    install_requires=read_requirements(),
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'black>=22.0.0',
            'flake8>=5.0.0',
            'mypy>=0.991',
        ],
        'build': [
            'pyinstaller>=5.0.0',
        ],
        'enhanced': [
            'Pillow>=9.0.0',
            'numpy>=1.21.0',
            'matplotlib>=3.5.0',
        ]
    },
    entry_points={
        'console_scripts': [
            'grey-scale-test=src.main:main',
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Topic :: Software Development :: Testing",
        "Topic :: System :: Hardware",
    ],
    python_requires=">=3.8",
    keywords="android testing automation grey-scale ltpo ltps",
    project_urls={
        "Bug Reports": "",
        "Source": "",
        "Documentation": "",
    },
)
