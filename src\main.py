"""
主程序入口
重构后的灰阶测试工具主程序
"""

import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.test_logic.ltpo_test import run_ltpo_test
from src.test_logic.ltps_test import run_ltps_test
from src.core.device_manager import DeviceManager
from src.utils.logger import setup_logging, restore_print, get_logger
from src.utils.config import config_manager
from src.utils.constants import SUCCESS_MESSAGES


class GreyScaleTestApp:
    """灰阶测试应用程序"""
    
    def __init__(self):
        self.logger = None
        self.log_file = None
        self.device_manager = None
        self.enable_inspection = True
        
    def initialize(self) -> bool:
        """
        初始化应用程序

        Returns:
            初始化是否成功
        """
        try:
            # 设置日志
            self.log_file = setup_logging()
            self.logger = get_logger()

            if self.logger:
                self.logger.debug(f"Python版本: {sys.version}")
                self.logger.debug(f"脚本路径: {os.path.abspath(__file__)}")
                self.logger.debug("开始初始化应用程序...")

            # 检查ADB是否可用
            import subprocess
            try:
                result = subprocess.run("adb version", shell=True, capture_output=True, text=True)
                if result.returncode != 0:
                    print("错误: 未找到ADB工具，请确保ADB已正确安装并添加到系统PATH中")
                    if self.logger:
                        self.logger.error("ADB工具未找到")
                    return False
                else:
                    if self.logger:
                        self.logger.debug(f"ADB版本: {result.stdout.strip()}")
            except Exception as e:
                print(f"错误: 无法检查ADB状态: {e}")
                if self.logger:
                    self.logger.error(f"ADB状态检查失败: {e}")
                return False

            # 初始化设备管理器
            self.device_manager = DeviceManager()

            if self.logger:
                self.logger.debug("应用程序初始化完成")

            return True

        except Exception as e:
            error_msg = f"应用程序初始化失败: {e}"
            print(error_msg)
            if self.logger:
                self.logger.error(error_msg, exc_info=True)
            return False
    
    def show_menu(self) -> None:
        """显示主菜单"""
        print("\n" + "="*50)
        print("灰阶自动化测试工具 v2.0")
        print("="*50)
        print("请选择操作:")
        print("1: LTPO灰阶测试")
        print("2: LTPS灰阶测试")
        print("3: 桌面LTPO灰阶测试")
        print("4: 桌面LTPS灰阶测试")
        print("5: 开启深色模式")
        print("6: 关闭深色模式")
        print("7: 切换智能帧率")
        print("8: 切换标准帧率")
        print("9: 切换高帧率")
        print("00: 切换灰阶校验状态")
        print("99: 数据收集诊断")
        print("0: 退出程序")
        print("="*50)

        # 显示当前状态
        inspection_status = "开启" if self.enable_inspection else "关闭"
        print(f"当前灰阶校验状态: {inspection_status}")
        print()
    
    def get_user_input(self) -> str:
        """
        获取用户输入
        
        Returns:
            用户输入的字符串
        """
        try:
            # 临时恢复原始print以显示提示符
            restore_print()
            user_input = input("请输入选项: ").strip()
            
            # 重新设置日志记录
            setup_logging()
            
            if self.logger:
                self.logger.info(f"用户输入: {user_input}")
            
            return user_input
            
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return "0"
        except Exception as e:
            if self.logger:
                self.logger.error(f"获取用户输入失败: {e}")
            return ""
    
    def handle_test_command(self, test_type: str, desktop_test: bool = False) -> None:
        """
        处理测试命令

        Args:
            test_type: 测试类型 ("ltpo" 或 "ltps")
            desktop_test: 是否为桌面测试
        """
        try:
            test_name = f"{'桌面' if desktop_test else ''}{test_type.upper()}灰阶测试"
            print(f"开始执行{test_name}...")

            if self.logger:
                self.logger.info(f"开始执行{test_name}")

            # 预检查：确保设备连接正常
            if not self._pre_test_check():
                print("测试前检查失败，无法继续执行测试")
                return

            # 执行测试
            if test_type.lower() == "ltpo":
                success = run_ltpo_test(desktop_test=desktop_test, enable_inspection=self.enable_inspection)
            elif test_type.lower() == "ltps":
                success = run_ltps_test(desktop_test=desktop_test, enable_inspection=self.enable_inspection)
            else:
                print(f"不支持的测试类型: {test_type}")
                return

            if success:
                print(f"✅ {test_name}完成！")
                if self.log_file:
                    print(f"📄 测试日志已保存至: {self.log_file}")
                    print(f"📊 测试结果已保存至: output/results/")
            else:
                print(f"❌ {test_name}失败！")
                print("请检查日志文件获取详细错误信息")

            # 等待用户确认
            restore_print()
            input("\n按回车键继续...")
            setup_logging()

        except Exception as e:
            error_msg = f"测试执行失败: {e}"
            if self.logger:
                self.logger.error(error_msg, exc_info=True)
            print(f"❌ {error_msg}")
            print("详细错误信息已记录到日志文件")

    def _pre_test_check(self) -> bool:
        """
        测试前检查

        Returns:
            检查是否通过
        """
        try:
            print("正在进行测试前检查...")

            # 检查设备连接
            from src.core.adb_controller import ADBController
            adb = ADBController()

            if not adb.check_device_connected():
                print("❌ 设备连接检查失败")
                print("请确保：")
                print("  1. Android设备已通过USB连接到电脑")
                print("  2. 设备已开启USB调试模式")
                print("  3. 已在设备上允许此电脑的调试权限")
                return False

            print("✅ 设备连接正常")

            # 检查设备权限
            try:
                result = adb.execute_command("adb shell whoami", capture_output=True)
                if result:
                    print(f"✅ 设备权限检查通过 (用户: {result})")
                else:
                    print("⚠️  无法获取设备权限信息，但将继续尝试")
            except Exception as e:
                print(f"⚠️  权限检查出现问题: {e}")

            # 检查并修复UIAutomator2问题
            print("🔧 检查UIAutomator2服务状态...")
            try:
                from src.utils.uiautomator_fix import fix_uiautomator2_issues

                fix_result = fix_uiautomator2_issues()

                if fix_result.get('success'):
                    print("✅ UIAutomator2服务状态正常")
                else:
                    print("⚠️  UIAutomator2服务存在问题，已尝试修复")
                    print(f"   状态: {fix_result.get('message', '未知')}")
                    print("   这可能会影响UI自动化功能，但不会阻止测试进行")

                    # 显示修复措施
                    fixes = fix_result.get('fixes_applied', [])
                    if fixes:
                        print("   已应用的修复措施:")
                        for fix in fixes[:3]:  # 只显示前3个
                            print(f"   - {fix}")

            except Exception as ui_error:
                print(f"⚠️  UIAutomator2检查失败: {ui_error}")
                print("   这不会影响测试的核心功能")

            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"测试前检查失败: {e}")
            print(f"❌ 测试前检查失败: {e}")
            return False
    
    def handle_device_command(self, command: str) -> None:
        """
        处理设备控制命令
        
        Args:
            command: 命令类型
        """
        try:
            if not self.device_manager:
                print("设备管理器未初始化")
                return
            
            if command == "dark_mode_on":
                self.device_manager.set_dark_mode(True)
                print("深色模式已开启")
            elif command == "dark_mode_off":
                self.device_manager.set_dark_mode(False)
                print("深色模式已关闭")
            elif command == "fps_smart":
                self.device_manager.set_fps_mode(1)
                print("已切换到智能帧率")
            elif command == "fps_60":
                self.device_manager.set_fps_mode(60)
                print("已切换到标准帧率")
            elif command == "fps_120":
                self.device_manager.set_fps_mode(120)
                print("已切换到高帧率")
            else:
                print(f"不支持的设备命令: {command}")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"执行设备命令失败: {e}")
            print(f"设备命令执行失败: {e}")
    
    def toggle_inspection(self) -> None:
        """切换灰阶校验状态"""
        self.enable_inspection = not self.enable_inspection
        status = "开启" if self.enable_inspection else "关闭"
        print(f"灰阶校验已{status}")

        if self.logger:
            self.logger.info(f"灰阶校验状态切换为: {status}")

    def run_data_collection_diagnosis(self) -> None:
        """运行数据收集诊断"""
        try:
            if self.logger:
                self.logger.info("启动数据收集诊断...")

            # 由于删除了comprehensive_diagnosis函数，这里改为简单的提示
            restore_print()
            print("数据收集诊断功能已简化，请确保：")
            print("1. 设备已正确连接")
            print("2. 已开启USB调试")
            print("3. 设备是VIVO品牌（或支持的品牌）")
            input("\n按回车键继续...")
            setup_logging()

        except Exception as e:
            if self.logger:
                self.logger.error(f"数据收集诊断失败: {e}")
            restore_print()
            print(f"❌ 诊断失败: {e}")
            input("按回车键继续...")
            setup_logging()
    
    def run(self) -> None:
        """运行主程序"""
        try:
            if not self.initialize():
                print("程序初始化失败")
                return
            
            print("灰阶自动化测试工具启动成功！")
            
            while True:
                self.show_menu()
                user_input = self.get_user_input()
                
                if user_input == '1':
                    self.handle_test_command("ltpo", desktop_test=False)
                elif user_input == '2':
                    self.handle_test_command("ltps", desktop_test=False)
                elif user_input == '3':
                    self.handle_test_command("ltpo", desktop_test=True)
                elif user_input == '4':
                    self.handle_test_command("ltps", desktop_test=True)
                elif user_input == '5':
                    self.handle_device_command("dark_mode_on")
                elif user_input == '6':
                    self.handle_device_command("dark_mode_off")
                elif user_input == '7':
                    self.handle_device_command("fps_smart")
                elif user_input == '8':
                    self.handle_device_command("fps_60")
                elif user_input == '9':
                    self.handle_device_command("fps_120")
                elif user_input == '00':
                    self.toggle_inspection()
                elif user_input == '99':
                    self.run_data_collection_diagnosis()
                elif user_input == '0':
                    print("正在退出程序...")
                    if self.log_file:
                        print(f"日志已保存至: {self.log_file}")
                    break
                else:
                    print("无效的选项，请重新输入")
                    
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            if self.logger:
                self.logger.error(f"程序运行时发生错误: {e}", exc_info=True)
            print(f"程序运行时发生错误: {e}")
            if self.log_file:
                print(f"详细错误信息已记录到日志文件: {self.log_file}")
        finally:
            # 清理资源
            self.cleanup()
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.device_manager:
                self.device_manager.cleanup()
            
            # 恢复原始print函数
            restore_print()
            
            if self.logger:
                self.logger.info("程序清理完成")
                
        except Exception as e:
            print(f"程序清理失败: {e}")


def main():
    """主函数"""
    app = GreyScaleTestApp()
    app.run()


if __name__ == '__main__':
    main()
